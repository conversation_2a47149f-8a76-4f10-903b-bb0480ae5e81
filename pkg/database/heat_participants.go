package database

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	"github.com/rbjoregren/segling/pkg/models"
)

// CreateHeatParticipant assigns a participant to a heat
func (db *DB) CreateHeatParticipant(heatID, participantID int64) error {
	now := time.Now()

	_, err := db.Exec(`
		INSERT INTO heat_participants (heat_id, participant_id, created_at)
		VALUES (?, ?, ?)
		ON CONFLICT(heat_id, participant_id) DO NOTHING
	`, heatID, participantID, now)

	return err
}

// DeleteHeatParticipant removes a participant from a heat
func (db *DB) DeleteHeatParticipant(heatID, participantID int64) error {
	_, err := db.Exec(`
		DELETE FROM heat_participants
		WHERE heat_id = ? AND participant_id = ?
	`, heatID, participantID)

	return err
}

// GetHeatParticipants returns all participants assigned to a specific heat
func (db *DB) GetHeatParticipants(heatID int64) ([]models.HeatParticipant, error) {
	rows, err := db.Query(`
		SELECT id, heat_id, participant_id, created_at
		FROM heat_participants
		WHERE heat_id = ?
		ORDER BY created_at
	`, heatID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var heatParticipants []models.HeatParticipant
	for rows.Next() {
		var hp models.HeatParticipant
		err := rows.Scan(&hp.ID, &hp.HeatID, &hp.ParticipantID, &hp.CreatedAt)
		if err != nil {
			return nil, err
		}
		heatParticipants = append(heatParticipants, hp)
	}

	return heatParticipants, nil
}

// GetParticipantHeats returns all heats a participant is assigned to
func (db *DB) GetParticipantHeats(participantID int64) ([]models.Heat, error) {
	rows, err := db.Query(`
		SELECT h.id, h.event_id, h.heat_number, h.name, h.start_time, h.created_at
		FROM heats h
		JOIN heat_participants hp ON h.id = hp.heat_id
		WHERE hp.participant_id = ?
		ORDER BY h.heat_number
	`, participantID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var heats []models.Heat
	for rows.Next() {
		var h models.Heat
		err := rows.Scan(&h.ID, &h.EventID, &h.HeatNumber, &h.Name, &h.StartTime, &h.CreatedAt)
		if err != nil {
			return nil, err
		}
		heats = append(heats, h)
	}

	return heats, nil
}

// ClearEventRotation removes all heat participant assignments for an event
func (db *DB) ClearEventRotation(eventID int64) error {
	_, err := db.Exec(`
		DELETE FROM heat_participants
		WHERE heat_id IN (
			SELECT id FROM heats WHERE event_id = ?
		)
	`, eventID)

	return err
}

// GenerateBoatRotation creates an optimal rotation schedule for boats across heats
func (db *DB) GenerateBoatRotation(eventID int64, boatsPerHeat, racesPerBoat int) error {
	// Get all participants for the event
	participants, err := db.GetEventParticipants(eventID)
	if err != nil {
		return fmt.Errorf("failed to get event participants: %w", err)
	}

	// Get all heats for the event
	heats, err := db.GetEventHeats(eventID)
	if err != nil {
		return fmt.Errorf("failed to get event heats: %w", err)
	}

	if len(participants) == 0 || len(heats) == 0 {
		return fmt.Errorf("event must have participants and heats to generate rotation")
	}

	// Clear existing rotation
	err = db.ClearEventRotation(eventID)
	if err != nil {
		return fmt.Errorf("failed to clear existing rotation: %w", err)
	}

	// If racesPerBoat is 0 or >= number of heats, assign all boats to all heats (no rotation)
	if racesPerBoat == 0 || racesPerBoat >= len(heats) {
		for _, heat := range heats {
			for _, participant := range participants {
				err = db.CreateHeatParticipant(heat.ID, participant.ID)
				if err != nil {
					return fmt.Errorf("failed to assign participant %d to heat %d: %w", participant.ID, heat.ID, err)
				}
			}
		}
		return nil
	}

	// Generate rotation using round-robin algorithm
	rotation := generateRoundRobinRotation(len(participants), len(heats), racesPerBoat, boatsPerHeat)

	// Apply the rotation
	for heatIndex, participantIndices := range rotation {
		if heatIndex >= len(heats) {
			break
		}
		heat := heats[heatIndex]

		for _, participantIndex := range participantIndices {
			if participantIndex >= len(participants) {
				continue
			}
			participant := participants[participantIndex]

			err = db.CreateHeatParticipant(heat.ID, participant.ID)
			if err != nil {
				return fmt.Errorf("failed to assign participant %d to heat %d: %w", participant.ID, heat.ID, err)
			}
		}
	}

	log.Printf("Generated boat rotation for event %d: %d participants across %d heats, %d races per boat, max %d boats per heat",
		eventID, len(participants), len(heats), racesPerBoat, boatsPerHeat)

	return nil
}

// generateRoundRobinRotation creates a fair rotation schedule
func generateRoundRobinRotation(numParticipants, numHeats, racesPerBoat, boatsPerHeat int) [][]int {
	rotation := make([][]int, numHeats)

	// If boatsPerHeat is 0 or larger than participants, use all participants
	if boatsPerHeat == 0 || boatsPerHeat > numParticipants {
		boatsPerHeat = numParticipants
	}

	// Simple round-robin distribution
	for heatIndex := 0; heatIndex < numHeats; heatIndex++ {
		heatParticipants := make([]int, 0, boatsPerHeat)

		// Count how many races each participant has been assigned so far
		participantRaceCounts := make([]int, numParticipants)
		for h := 0; h < heatIndex; h++ {
			for _, p := range rotation[h] {
				if p < numParticipants {
					participantRaceCounts[p]++
				}
			}
		}

		// Add participants to this heat, prioritizing those with fewer races
		for len(heatParticipants) < boatsPerHeat && len(heatParticipants) < numParticipants {
			// Find participant with fewest races who can still race more
			bestParticipant := -1
			minRaces := racesPerBoat + 1

			for i := 0; i < numParticipants; i++ {
				// Check if this participant is already in this heat
				alreadyInHeat := false
				for _, p := range heatParticipants {
					if p == i {
						alreadyInHeat = true
						break
					}
				}

				if !alreadyInHeat && participantRaceCounts[i] < racesPerBoat && participantRaceCounts[i] < minRaces {
					minRaces = participantRaceCounts[i]
					bestParticipant = i
				}
			}

			if bestParticipant == -1 {
				break // No more participants can be added
			}

			heatParticipants = append(heatParticipants, bestParticipant)
			participantRaceCounts[bestParticipant]++
		}

		rotation[heatIndex] = heatParticipants
	}

	return rotation
}

// GetEventRotationMatrix returns a matrix showing which participants are in which heats
func (db *DB) GetEventRotationMatrix(eventID int64) ([]models.EventParticipant, []models.Heat, [][]bool, error) {
	// Get all participants for the event
	participants, err := db.GetEventParticipants(eventID)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("failed to get event participants: %w", err)
	}

	// Get all heats for the event
	heats, err := db.GetEventHeats(eventID)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("failed to get event heats: %w", err)
	}

	// Create matrix
	matrix := make([][]bool, len(participants))
	for i := range matrix {
		matrix[i] = make([]bool, len(heats))
	}

	// Fill matrix with heat participant data
	for heatIndex, heat := range heats {
		heatParticipants, err := db.GetHeatParticipants(heat.ID)
		if err != nil {
			return nil, nil, nil, fmt.Errorf("failed to get participants for heat %d: %w", heat.ID, err)
		}

		for _, hp := range heatParticipants {
			// Find participant index
			for participantIndex, participant := range participants {
				if participant.ID == hp.ParticipantID {
					matrix[participantIndex][heatIndex] = true
					break
				}
			}
		}
	}

	return participants, heats, matrix, nil
}

// UpdateEventRotationSettings updates the rotation settings for an event
func (db *DB) UpdateEventRotationSettings(eventID int64, rotationEnabled bool, boatsPerHeat, racesPerBoat int) error {
	now := time.Now()

	// Check if rotation columns exist
	var hasRotationEnabled int
	err := db.QueryRow(`
		SELECT COUNT(*) FROM pragma_table_info('events')
		WHERE name = 'rotation_enabled'
	`).Scan(&hasRotationEnabled)
	if err != nil {
		return err
	}

	if hasRotationEnabled == 0 {
		return fmt.Errorf("rotation columns not found in events table")
	}

	_, err = db.Exec(`
		UPDATE events
		SET rotation_enabled = ?, boats_per_heat = ?, races_per_boat = ?, updated_at = ?
		WHERE id = ?
	`, rotationEnabled, boatsPerHeat, racesPerBoat, now, eventID)

	return err
}

// GetHeatParticipantsForEvent returns participants assigned to a specific heat, or all participants if rotation is disabled
func (db *DB) GetHeatParticipantsForEvent(eventID, heatID int64) ([]models.EventParticipant, error) {
	// Get the event to check if rotation is enabled
	event, err := db.GetEvent(eventID)
	if err != nil {
		return nil, fmt.Errorf("failed to get event: %w", err)
	}

	// If rotation is not enabled or no specific heat is selected, return all participants
	if !event.RotationEnabled || heatID == 0 {
		return db.GetEventParticipants(eventID)
	}

	// Get participants assigned to this specific heat
	rows, err := db.Query(`
		SELECT DISTINCT ep.id, ep.event_id, ep.sailor_id, ep.boat_id, ep.personal_number,
		       ep.srs_type, ep.selected_srs_value, ep.custom_srs_value, ep.use_custom_srs_value,
		       ep.finish_time, ep.crew_count, ep.dns, ep.dnf, ep.created_at, ep.updated_at
		FROM event_participants ep
		JOIN heat_participants hp ON ep.id = hp.participant_id
		WHERE ep.event_id = ? AND hp.heat_id = ?
		ORDER BY ep.id
	`, eventID, heatID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var participants []models.EventParticipant
	for rows.Next() {
		var p models.EventParticipant
		var personalNumber sql.NullString
		var finishTime sql.NullString
		var srsType sql.NullString
		var selectedSRSValue sql.NullFloat64
		var customSRSValue sql.NullFloat64
		var useCustomSRSValue sql.NullBool
		var crewCount sql.NullInt64
		var dns sql.NullBool
		var dnf sql.NullBool

		err := rows.Scan(&p.ID, &p.EventID, &p.SailorID, &p.BoatID, &personalNumber,
			&srsType, &selectedSRSValue, &customSRSValue, &useCustomSRSValue,
			&finishTime, &crewCount, &dns, &dnf, &p.CreatedAt, &p.UpdatedAt)
		if err != nil {
			return nil, err
		}

		// Handle nullable fields
		if personalNumber.Valid {
			p.PersonalNumber = personalNumber.String
		}
		if finishTime.Valid {
			p.FinishTime = finishTime.String
		}
		if srsType.Valid {
			p.SRSType = srsType.String
		} else {
			p.SRSType = "srs" // Default value
		}
		if selectedSRSValue.Valid {
			p.SelectedSRSValue = selectedSRSValue.Float64
		}
		if customSRSValue.Valid {
			p.CustomSRSValue = customSRSValue.Float64
		}
		if useCustomSRSValue.Valid {
			p.UseCustomSRSValue = useCustomSRSValue.Bool
		}
		if crewCount.Valid {
			p.CrewCount = int(crewCount.Int64)
		}
		if dns.Valid {
			p.DNS = dns.Bool
		}
		if dnf.Valid {
			p.DNF = dnf.Bool
		}

		participants = append(participants, p)
	}

	return participants, nil
}
