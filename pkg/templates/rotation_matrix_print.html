<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Båtrotationsmatris - {{.event.Namn}}</title>
    <style>
        @media print {
            @page {
                size: landscape;
                margin: 0.5in;
            }
            body {
                font-size: 10pt;
                line-height: 1.2;
            }
            .no-print {
                display: none;
            }
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .event-info {
            margin-bottom: 15px;
        }
        
        .rotation-matrix {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .rotation-matrix th,
        .rotation-matrix td {
            border: 1px solid #333;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }
        
        .rotation-matrix th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        
        .participant-info {
            text-align: left;
            font-weight: bold;
        }
        
        .heat-header {
            writing-mode: vertical-lr;
            text-orientation: mixed;
            min-width: 30px;
        }
        
        .no-race {
            background-color: #e0e0e0;
            font-weight: bold;
            font-size: 14pt;
        }
        
        .race-cell {
            background-color: white;
            min-height: 30px;
            width: 40px;
        }
        
        .instructions {
            margin-top: 20px;
            padding: 10px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .print-button {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="no-print print-button">
        <button onclick="window.print()" class="btn btn-primary">
            <i class="fas fa-print"></i> Skriv ut
        </button>
        <button onclick="window.close()" class="btn btn-secondary">
            Stäng
        </button>
    </div>

    <div class="header">
        <h1>Båtrotationsmatris</h1>
        <h2>{{.event.Namn}}</h2>
        <div class="event-info">
            <strong>Datum:</strong> {{.event.Datum.Format "2006-01-02"}} |
            <strong>Typ:</strong> {{.event.Tavlingstyp}}
            {{if .event.RotationEnabled}}
            | <strong>Rotation:</strong> {{.event.BoatsPerHeat}} båtar per heat, {{.event.RacesPerBoat}} lopp per båt
            {{end}}
        </div>
    </div>

    <table class="rotation-matrix">
        <thead>
            <tr>
                <th class="participant-info">Seglare / Båt</th>
                {{range .heats}}
                <th class="heat-header">{{.Name}}</th>
                {{end}}
            </tr>
        </thead>
        <tbody>
            {{range $participantIndex, $participant := .participants}}
            <tr>
                <td class="participant-info">
                    <strong>{{$participant.sailor.Namn}}</strong><br>
                    <small>{{$participant.boat}}</small>
                </td>
                {{range $heatIndex, $heat := $.heats}}
                {{$participates := index $participant.matrix $heatIndex}}
                {{if $participates}}
                <td class="race-cell">
                    <!-- Empty cell for manual placement entry -->
                </td>
                {{else}}
                <td class="no-race">X</td>
                {{end}}
                {{end}}
            </tr>
            {{end}}
        </tbody>
    </table>

    <div class="instructions">
        <h3>Instruktioner:</h3>
        <ul>
            <li><strong>X</strong> = Båten deltar INTE i denna deltävling</li>
            <li><strong>Tom ruta</strong> = Båten deltar i deltävlingen - skriv in placering efter loppet</li>
            <li>Använd denna matris för att hålla koll på vilka båtar som ska starta i varje heat</li>
            <li>Fyll i placeringar manuellt under tävlingen och för sedan över resultaten till systemet</li>
        </ul>
    </div>

    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
