{{template "header.html" .}}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            {{template "event_nav.html" .}}
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2>Deltävlingar - {{.event.Namn}}</h2>
                    <div>
                        {{if gt (len .heats) 1}}
                        <button type="button" class="btn btn-info me-2" data-bs-toggle="modal" data-bs-target="#rotationModal">
                            <i class="fas fa-sync-alt"></i> Båtrotation
                        </button>
                        {{if .event.RotationEnabled}}
                        <a href="/events/{{.event.ID}}/rotation-matrix/print" target="_blank" class="btn btn-secondary me-2">
                            <i class="fas fa-print"></i> Skriv ut rotationsmatris
                        </a>
                        {{end}}
                        {{end}}
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addHeatModal">
                            <i class="fas fa-plus"></i> Lägg till deltävling
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {{if .heats}}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Nummer</th>
                                        <th>Namn</th>
                                        {{ if not .event.Entypsegling }}
                                        <th>Starttid</th>
                                        {{ end }}
                                        <th>Skapad</th>
                                        <th>Åtgärder</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {{range .heats}}
                                    <tr>
                                        <td>{{.HeatNumber}}</td>
                                        <td>{{.Name}}</td>
                                        {{ if not $.event.Entypsegling }}
                                        <td>
                                            <input type="time"
                                                   class="form-control form-control-sm heat-start-time"
                                                   value="{{.StartTime}}"
                                                   data-heat-id="{{.ID}}"
                                                   style="width: 120px;">
                                        </td>
                                        {{ end }}
                                        <td>{{.CreatedAt.Format "2006-01-02 15:04"}}</td>
                                        <td>
                                            {{ if $.event.Entypsegling }}
                                            <a href="/events/{{$.event.ID}}/finish-times?heat={{.ID}}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-sort-numeric-down"></i> Placeringar
                                            </a>
                                            {{ else }}
                                            <a href="/events/{{$.event.ID}}/finish-times?heat={{.ID}}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-clock"></i> Måltider
                                            </a>
                                            {{ end }}
                                            <a href="/heats/{{.ID}}/results" class="btn btn-sm btn-outline-success">
                                                <i class="fas fa-trophy"></i> Resultat
                                            </a>
                                            {{if gt (len $.heats) 1}}
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    onclick="deleteHeat({{.ID}}, {{$.event.ID}})">
                                                <i class="fas fa-trash"></i> Ta bort
                                            </button>
                                            {{end}}
                                        </td>
                                    </tr>
                                    {{end}}
                                </tbody>
                            </table>
                        </div>
                    {{else}}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Inga deltävlingar hittades. Lägg till en deltävling för att komma igång.
                        </div>
                    {{end}}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Heat Modal -->
<div class="modal fade" id="addHeatModal" tabindex="-1" aria-labelledby="addHeatModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="/events/{{.event.ID}}/heats">
                <div class="modal-header">
                    <h5 class="modal-title" id="addHeatModalLabel">Lägg till deltävling</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="heat_number" class="form-label">Nummer</label>
                        <input type="number" class="form-control" id="heat_number" name="heat_number"
                               value="{{add (len .heats) 1}}" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label for="name" class="form-label">Namn</label>
                        <input type="text" class="form-control" id="name" name="name"
                               value="Heat {{add (len .heats) 1}}" required>
                    </div>
                    {{ if not .event.Entypsegling }}
                    <div class="mb-3">
                        <label for="start_time" class="form-label">Starttid</label>
                        <input type="time" class="form-control" id="start_time" name="start_time"
                               value="{{if .event.Starttid}}{{.event.Starttid}}{{else}}10:00{{end}}" required>
                        <div class="form-text">Starttid för denna deltävling</div>
                    </div>
                    {{ end }}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Avbryt</button>
                    <button type="submit" class="btn btn-primary">Lägg till</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Boat Rotation Modal -->
<div class="modal fade" id="rotationModal" tabindex="-1" aria-labelledby="rotationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="/events/{{.event.ID}}/rotation">
                <div class="modal-header">
                    <h5 class="modal-title" id="rotationModalLabel">Båtrotation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="rotation_enabled" name="rotation_enabled"
                               {{if .event.RotationEnabled}}checked{{end}} onchange="toggleRotationSettings()">
                        <label class="form-check-label" for="rotation_enabled">
                            Aktivera båtrotation
                        </label>
                        <div class="form-text">
                            Aktivera för att rotera båtar mellan deltävlingar när det finns för många båtar per heat.
                        </div>
                    </div>

                    <div id="rotationSettings" style="{{if not .event.RotationEnabled}}display: none;{{end}}">
                        <div class="mb-3">
                            <label for="boats_per_heat" class="form-label">Båtar per deltävling</label>
                            <input type="number" class="form-control" id="boats_per_heat" name="boats_per_heat"
                                   value="{{if .event.BoatsPerHeat}}{{.event.BoatsPerHeat}}{{else}}6{{end}}" min="1" max="50">
                            <div class="form-text">Maximalt antal båtar som ska tävla i varje deltävling</div>
                        </div>
                        <div class="mb-3">
                            <label for="races_per_boat" class="form-label">Lopp per båt</label>
                            <input type="number" class="form-control" id="races_per_boat" name="races_per_boat"
                                   value="{{if .event.RacesPerBoat}}{{.event.RacesPerBoat}}{{else}}2{{end}}" min="1" max="{{len .heats}}">
                            <div class="form-text">Antal deltävlingar varje båt ska delta i</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Avbryt</button>
                    <button type="submit" class="btn btn-primary">Spara rotation</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleRotationSettings() {
    const checkbox = document.getElementById('rotation_enabled');
    const settings = document.getElementById('rotationSettings');
    const boatsPerHeat = document.getElementById('boats_per_heat');
    const racesPerBoat = document.getElementById('races_per_boat');

    if (checkbox.checked) {
        settings.style.display = 'block';
        // Enable validation for the inputs
        boatsPerHeat.required = true;
        racesPerBoat.required = true;
    } else {
        settings.style.display = 'none';
        // Disable validation for the inputs
        boatsPerHeat.required = false;
        racesPerBoat.required = false;
    }
}

// Function to delete a heat
function deleteHeat(heatId, eventId) {
    if (confirm('Är du säker på att du vill ta bort denna deltävling?')) {
        fetch(`/heats/${heatId}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (response.ok) {
                // Redirect back to heats page
                window.location.href = `/events/${eventId}/heats`;
            } else {
                return response.json().then(data => {
                    throw new Error(data.error || 'Ett fel uppstod vid borttagning av deltävlingen');
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Fel vid borttagning av deltävling: ' + error.message);
        });
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Handle start time changes
    document.querySelectorAll('.heat-start-time').forEach(function(input) {
        input.addEventListener('change', function() {
            const heatId = this.getAttribute('data-heat-id');
            const startTime = this.value;

            if (!startTime) {
                return;
            }

            // Create form data
            const formData = new FormData();
            formData.append('start_time', startTime);

            // Send update request
            fetch(`/heats/${heatId}/start-time`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert('Fel vid uppdatering av starttid: ' + data.error);
                    // Revert the input value
                    this.value = this.defaultValue;
                } else {
                    // Show success feedback
                    this.style.backgroundColor = '#d4edda';
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                    }, 1000);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Fel vid uppdatering av starttid');
                // Revert the input value
                this.value = this.defaultValue;
            });
        });
    });
});
</script>

{{template "footer.html" .}}
