package models

import (
	"time"
)

// Sailor represents a sailor in the system
type Sailor struct {
	ID        int64     `json:"id"`
	Namn      string    `json:"namn"`
	Telefon   string    `json:"telefon"`
	Klubb     string    `json:"klubb"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// Boat represents a boat in the system
type Boat struct {
	ID                               int64     `json:"id"`
	Namn                             string    `json:"namn"`
	Battyp                           string    `json:"battyp"`
	MatbrevsNummer                   string    `json:"matbrevs_nummer"`
	Segelnummer                      string    `json:"segelnummer"`
	Nationality                      string    `json:"nationality"`
	SRS                              float64   `json:"srs"`
	SRSUtanUndanvindsegel            float64   `json:"srs_utan_undanvindsegel"`
	SRSShorthanded                   float64   `json:"srs_shorthanded"`
	SRSShorthandedUtanUndanvindsegel float64   `json:"srs_shorthanded_utan_undanvindsegel"`
	CreatedAt                        time.Time `json:"created_at"`
	UpdatedAt                        time.Time `json:"updated_at"`
}

// Event represents a sailing event
type Event struct {
	ID                int64     `json:"id"`
	Namn              string    `json:"namn"`
	Datum             time.Time `json:"datum"`
	Starttid          string    `json:"starttid"`
	Vind              int       `json:"vind"`
	Banlangd          int       `json:"banlangd"`
	Jaktstart         bool      `json:"jaktstart"`      // Legacy field - kept for backward compatibility
	JaktstartType     string    `json:"jaktstart_type"` // New field: "regular", "half", "entypsegling", "none"
	Tavlingstyp       string    `json:"tavlingstyp"`    // Type of competition (e.g., Kvällssegling, Regatta)
	Entypsegling      bool      `json:"entypsegling"`   // Legacy field - kept for backward compatibility
	BoatType          string    `json:"boat_type"`      // Boat type for entypsegling events (e.g., "Laser", "Optimist")
	Beskrivning       string    `json:"beskrivning"`
	Locked            bool      `json:"locked"`              // Whether the event is locked (results finalized)
	DiscardAfterHeats int       `json:"discard_after_heats"` // Number of heats after which worst results can be discarded (0 = no discard)
	// Boat rotation settings
	RotationEnabled  bool      `json:"rotation_enabled"` // Whether boat rotation is enabled for this event
	BoatsPerHeat     int       `json:"boats_per_heat"`   // Maximum number of boats per heat (0 = no limit)
	RacesPerBoat     int       `json:"races_per_boat"`   // Number of heats each boat should participate in (0 = all heats)
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
	ParticipantCount int       `json:"participant_count"` // Number of participants in the event
	BoatCount        int       `json:"boat_count"`        // Number of unique boats in the event
}

// JaktstartType constants
const (
	JaktstartTypeNone         = "none"         // Regular sailing, no jaktstart
	JaktstartTypeRegular      = "regular"      // Full jaktstart - boats meet at finish line
	JaktstartTypeHalf         = "half"         // Half jaktstart - boats meet at half course distance
	JaktstartTypeEntypsegling = "entypsegling" // Entypsegling - placement-based scoring, no SRS
)

// Helper methods for Event

// IsJaktstart returns true if the event uses any form of jaktstart (regular or half)
func (e *Event) IsJaktstart() bool {
	return e.JaktstartType == JaktstartTypeRegular || e.JaktstartType == JaktstartTypeHalf
}

// IsEntypsegling returns true if the event is an entypsegling event
func (e *Event) IsEntypsegling() bool {
	return e.JaktstartType == JaktstartTypeEntypsegling
}

// IsHalfJaktstart returns true if the event uses half jaktstart
func (e *Event) IsHalfJaktstart() bool {
	return e.JaktstartType == JaktstartTypeHalf
}

// GetJaktstartMultiplier returns the multiplier for jaktstart calculations
// 1.0 for regular jaktstart, 0.5 for half jaktstart, 0.0 for no jaktstart
func (e *Event) GetJaktstartMultiplier() float64 {
	switch e.JaktstartType {
	case JaktstartTypeRegular:
		return 1.0
	case JaktstartTypeHalf:
		return 0.5
	default:
		return 0.0
	}
}

// IsDiscardEnabled returns true if heat discard is enabled for this event
func (e *Event) IsDiscardEnabled() bool {
	return e.DiscardAfterHeats > 0
}

// CanDiscardHeats returns true if the event has enough heats to allow discard
func (e *Event) CanDiscardHeats(totalHeats int) bool {
	return e.IsDiscardEnabled() && totalHeats >= e.DiscardAfterHeats
}

// GetDiscardCount returns the number of heats that should be discarded
func (e *Event) GetDiscardCount(totalHeats int) int {
	if !e.CanDiscardHeats(totalHeats) {
		return 0
	}
	// For now, discard 1 heat for every DiscardAfterHeats completed
	// This can be made more sophisticated later if needed
	return totalHeats / e.DiscardAfterHeats
}

// Heat represents a heat/deltävling within an event
type Heat struct {
	ID         int64     `json:"id"`
	EventID    int64     `json:"event_id"`
	HeatNumber int       `json:"heat_number"`
	Name       string    `json:"name"`
	StartTime  string    `json:"start_time"` // Start time for this heat (HH:MM format)
	CreatedAt  time.Time `json:"created_at"`
}

// HeatParticipant represents which participants are assigned to which heats (for boat rotation)
type HeatParticipant struct {
	ID            int64     `json:"id"`
	HeatID        int64     `json:"heat_id"`
	ParticipantID int64     `json:"participant_id"`
	CreatedAt     time.Time `json:"created_at"`
}

// EventParticipant represents a sailor participating in an event with a boat
type EventParticipant struct {
	ID                int64     `json:"id"`
	EventID           int64     `json:"event_id"`
	SailorID          int64     `json:"sailor_id"`
	BoatID            int64     `json:"boat_id"`         // Nullable for entypsegling events
	PersonalNumber    string    `json:"personal_number"` // Personal number for entypsegling events (e.g., "SWE 123")
	SRSType           string    `json:"srs_type"`
	SelectedSRSValue  float64   `json:"selected_srs_value"`
	CustomSRSValue    float64   `json:"custom_srs_value"`
	UseCustomSRSValue bool      `json:"use_custom_srs_value"`
	FinishTime        string    `json:"finish_time"`
	CrewCount         int       `json:"crew_count"`
	DNS               bool      `json:"dns"` // Did Not Start
	DNF               bool      `json:"dnf"` // Did Not Finish
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// HeatFinishTime represents a finish time for a participant in a specific heat
type HeatFinishTime struct {
	ID            int64     `json:"id"`
	HeatID        int64     `json:"heat_id"`
	ParticipantID int64     `json:"participant_id"`
	FinishTime    string    `json:"finish_time"`
	Placement     int       `json:"placement"` // Placement for entypsegling events (1st, 2nd, 3rd, etc.)
	DNS           bool      `json:"dns"`       // Did Not Start
	DNF           bool      `json:"dnf"`       // Did Not Finish
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// SRSBoatType represents a boat type from the SRS BoatList
type SRSBoatType struct {
	ID                               int64     `json:"id"`
	Battyp                           string    `json:"battyp"`
	SRS                              float64   `json:"srs"`
	SRSUtanUndanvindsegel            float64   `json:"srs_utan_undanvindsegel"`
	SRSShorthanded                   float64   `json:"srs_shorthanded"`
	SRSShorthandedUtanUndanvindsegel float64   `json:"srs_shorthanded_utan_undanvindsegel"`
	CreatedAt                        time.Time `json:"created_at"`
	UpdatedAt                        time.Time `json:"updated_at"`
}

// SRSMatbrev represents a boat from the SRS ApprovedList with a measurement certificate
type SRSMatbrev struct {
	ID                               int64     `json:"id"`
	MatbrevsNummer                   string    `json:"matbrevs_nummer"`
	Battyp                           string    `json:"battyp"`
	BatNamn                          string    `json:"bat_namn"`
	Agare                            string    `json:"agare"`
	Segelnummer                      string    `json:"segelnummer"`
	Nationality                      string    `json:"nationality"`
	SRS                              float64   `json:"srs"`
	SRSUtanUndanvindsegel            float64   `json:"srs_utan_undanvindsegel"`
	SRSShorthanded                   float64   `json:"srs_shorthanded"`
	SRSShorthandedUtanUndanvindsegel float64   `json:"srs_shorthanded_utan_undanvindsegel"`
	CreatedAt                        time.Time `json:"created_at"`
	UpdatedAt                        time.Time `json:"updated_at"`
}

// SRSSyncLog represents a log entry for SRS data synchronization
type SRSSyncLog struct {
	ID        int64     `json:"id"`
	Source    string    `json:"source"`
	Status    string    `json:"status"`
	Message   string    `json:"message"`
	CreatedAt time.Time `json:"created_at"`
}

// Result represents a race result for a participant
type Result struct {
	EventParticipant
	Sailor                Sailor  `json:"sailor"`
	Boat                  Boat    `json:"boat"`
	StartTime             string  `json:"start_time"`
	ElapsedTime           string  `json:"elapsed_time"`
	CorrectedTime         string  `json:"corrected_time"`
	TimeToPrevious        string  `json:"time_to_previous"`        // Time difference to the previous boat
	TimeToWinner          string  `json:"time_to_winner"`          // Time difference to the winner
	ElapsedSeconds        int     `json:"elapsed_seconds"`         // Elapsed time in seconds (for tiebreaker)
	CorrectedSeconds      int     `json:"corrected_seconds"`       // Corrected time in seconds (for calculations)
	CorrectedSecondsFloat float64 `json:"corrected_seconds_float"` // Full precision corrected time for accurate sorting
	TotalPersons          int     `json:"total_persons"`           // Total number of persons (skipper + crew)
	DNS                   bool    `json:"dns"`                     // Did Not Start
	DNF                   bool    `json:"dnf"`                     // Did Not Finish
}

// HeatResult represents a race result for a participant in a specific heat
type HeatResult struct {
	Heat                  Heat             `json:"heat"`
	EventParticipant      EventParticipant `json:"event_participant"`
	Sailor                Sailor           `json:"sailor"`
	Boat                  Boat             `json:"boat"`
	FinishTime            string           `json:"finish_time"`
	StartTime             string           `json:"start_time"`
	ElapsedTime           string           `json:"elapsed_time"`
	CorrectedTime         string           `json:"corrected_time"`
	TimeToPrevious        string           `json:"time_to_previous"`        // Time difference to the previous boat
	TimeToWinner          string           `json:"time_to_winner"`          // Time difference to the winner
	ElapsedSeconds        int              `json:"elapsed_seconds"`         // Elapsed time in seconds (for tiebreaker)
	CorrectedSeconds      int              `json:"corrected_seconds"`       // Corrected time in seconds (for calculations)
	CorrectedSecondsFloat float64          `json:"corrected_seconds_float"` // Full precision corrected time for accurate sorting
	TotalPersons          int              `json:"total_persons"`           // Total number of persons (skipper + crew)
	Position              int              `json:"position"`                // Position in this heat
	Points                float64          `json:"points"`                  // Points for this heat (position-based, with decimal precision for ties)
	DNS                   bool             `json:"dns"`                     // Did Not Start
	DNF                   bool             `json:"dnf"`                     // Did Not Finish
}

// TotalResult represents combined results across all heats for a participant
type TotalResult struct {
	EventParticipant EventParticipant `json:"event_participant"`
	Sailor           Sailor           `json:"sailor"`
	Boat             Boat             `json:"boat"`
	HeatResults      []HeatResult     `json:"heat_results"`    // Results for each heat
	TotalPoints      float64          `json:"total_points"`    // Sum of points across all heats (with decimal precision for ties)
	TotalPosition    int              `json:"total_position"`  // Overall position based on total points
	DiscardedHeats   []int64          `json:"discarded_heats"` // IDs of heats that were discarded for this participant
}

// SavedResult represents a finalized race result stored in the database
type SavedResult struct {
	ID                    int64     `json:"id"`
	EventID               int64     `json:"event_id"`
	Position              int       `json:"position"`
	SailorID              int64     `json:"sailor_id"`
	SailorName            string    `json:"sailor_name"`
	SailorClub            string    `json:"sailor_club"`
	BoatID                int64     `json:"boat_id"`
	BoatName              string    `json:"boat_name"`
	BoatType              string    `json:"boat_type"`
	MatbrevsNummer        string    `json:"matbrevs_nummer"`
	Segelnummer           string    `json:"segelnummer"`
	Nationality           string    `json:"nationality"`
	SRSValue              float64   `json:"srs_value"`
	SRSType               string    `json:"srs_type"`
	UseCustomSRSValue     bool      `json:"use_custom_srs_value"`
	CrewCount             int       `json:"crew_count"`
	StartTime             string    `json:"start_time"`
	FinishTime            string    `json:"finish_time"`
	ElapsedTime           string    `json:"elapsed_time"`
	CorrectedTime         string    `json:"corrected_time"`
	TimeToPrevious        string    `json:"time_to_previous"`
	TimeToWinner          string    `json:"time_to_winner"`
	ElapsedSeconds        int       `json:"elapsed_seconds"`         // Elapsed time in seconds (for tiebreaker)
	CorrectedSeconds      int       `json:"corrected_seconds"`       // Corrected time in seconds (for calculations)
	CorrectedSecondsFloat float64   `json:"corrected_seconds_float"` // Full precision corrected time for accurate sorting
	DNS                   bool      `json:"dns"`                     // Did Not Start
	DNF                   bool      `json:"dnf"`                     // Did Not Finish
	CreatedAt             time.Time `json:"created_at"`
}
