package handlers

import (
	"fmt"
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/rbjoregren/segling/pkg/models"
)

// GetEventHeats handles the event heats management page
func (h *Handler) GetEventHeats(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Invalid event ID",
		})
		return
	}

	// Get the event
	event, err := h.DB.GetEvent(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get heats for this event
	heats, err := h.DB.GetEventHeats(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get all events for the dropdown
	allEvents, err := h.DB.GetEvents()
	if err != nil {
		// If there's an error, just continue with an empty list
		allEvents = []models.Event{}
	}

	c.HTML(http.StatusOK, "event_heats.html", gin.H{
		"title":                  "Deltävlingar - " + event.Namn,
		"event":                  event,
		"heats":                  heats,
		"allEvents":              allEvents,
		"selectedEventID":        id,
		"selectedEventJaktstart": event.Jaktstart,
		"activeMenu":             "heats",
	})
}

// CreateHeat handles creating a new heat for an event
func (h *Handler) CreateHeat(c *gin.Context) {
	idStr := c.Param("id")
	eventID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event ID"})
		return
	}

	// Get the event to check if it's entypsegling
	event, err := h.DB.GetEvent(eventID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get event details"})
		return
	}

	// Get form data
	heatNumberStr := c.PostForm("heat_number")
	name := c.PostForm("name")
	startTime := c.PostForm("start_time")

	heatNumber, err := strconv.Atoi(heatNumberStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid heat number"})
		return
	}

	// For entypsegling events, start time is not needed
	if event.Entypsegling {
		startTime = "" // No start time for entypsegling events
	} else {
		// If no start time provided for regular events, use a default
		if startTime == "" {
			startTime = "10:00"
		}
	}

	// Create the heat
	_, err = h.DB.CreateHeat(eventID, heatNumber, name, startTime)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Redirect back to the heats page
	c.Redirect(http.StatusSeeOther, "/events/"+idStr+"/heats")
}

// UpdateEventRotation handles updating boat rotation settings for an event
func (h *Handler) UpdateEventRotation(c *gin.Context) {
	idStr := c.Param("id")
	eventID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event ID"})
		return
	}

	// Get form data
	rotationEnabled := c.PostForm("rotation_enabled") == "on"
	boatsPerHeatStr := c.PostForm("boats_per_heat")
	racesPerBoatStr := c.PostForm("races_per_boat")

	var boatsPerHeat, racesPerBoat int
	if rotationEnabled {
		boatsPerHeat, err = strconv.Atoi(boatsPerHeatStr)
		if err != nil || boatsPerHeat < 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid boats per heat value"})
			return
		}

		racesPerBoat, err = strconv.Atoi(racesPerBoatStr)
		if err != nil || racesPerBoat < 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid races per boat value"})
			return
		}
	}

	// Update the event with rotation settings
	err = h.DB.UpdateEventRotationSettings(eventID, rotationEnabled, boatsPerHeat, racesPerBoat)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update rotation settings: " + err.Error()})
		return
	}

	// If rotation is enabled, generate the rotation
	if rotationEnabled {
		err = h.DB.GenerateBoatRotation(eventID, boatsPerHeat, racesPerBoat)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate boat rotation: " + err.Error()})
			return
		}
	} else {
		// If rotation is disabled, clear any existing rotation
		err = h.DB.ClearEventRotation(eventID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to clear rotation: " + err.Error()})
			return
		}
	}

	// Redirect back to the heats page
	c.Redirect(http.StatusSeeOther, "/events/"+idStr+"/heats")
}

// PrintRotationMatrix handles printing the boat rotation matrix
func (h *Handler) PrintRotationMatrix(c *gin.Context) {
	idStr := c.Param("id")
	eventID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{"error": "Invalid event ID"})
		return
	}

	// Get the event
	event, err := h.DB.GetEvent(eventID)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{"error": "Failed to get event details"})
		return
	}

	// Get rotation matrix
	participants, heats, matrix, err := h.DB.GetEventRotationMatrix(eventID)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{"error": "Failed to get rotation matrix"})
		return
	}

	// Get sailor information for each participant
	participantDetails := make([]map[string]interface{}, len(participants))
	for i, participant := range participants {
		sailor, err := h.DB.GetSailor(participant.SailorID)
		if err != nil {
			continue
		}

		var boatInfo string
		if event.Entypsegling {
			boatInfo = participant.PersonalNumber
		} else {
			boat, err := h.DB.GetBoat(participant.BoatID)
			if err != nil {
				boatInfo = "Unknown Boat"
			} else {
				boatInfo = fmt.Sprintf("%s-%s", boat.Nationality, boat.Segelnummer)
			}
		}

		participantDetails[i] = map[string]interface{}{
			"sailor": sailor,
			"boat":   boatInfo,
			"matrix": matrix[i],
		}
	}

	c.HTML(http.StatusOK, "rotation_matrix_print.html", gin.H{
		"event":        event,
		"heats":        heats,
		"participants": participantDetails,
	})
}

// DeleteHeat handles deleting a heat
func (h *Handler) DeleteHeat(c *gin.Context) {
	idStr := c.Param("id")
	log.Printf("DEBUG: DeleteHeat called with ID: %s, Method: %s", idStr, c.Request.Method)

	heatID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		log.Printf("ERROR: Invalid heat ID: %s", idStr)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid heat ID"})
		return
	}

	// Get the heat to find the event ID for redirect
	heat, err := h.DB.GetHeat(heatID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Delete the heat
	err = h.DB.DeleteHeat(heatID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Redirect back to the heats page
	c.Redirect(http.StatusSeeOther, "/events/"+strconv.FormatInt(heat.EventID, 10)+"/heats")
}

// GetEventHeatsAPI returns heats for an event as JSON
func (h *Handler) GetEventHeatsAPI(c *gin.Context) {
	idStr := c.Param("id")
	eventID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event ID"})
		return
	}

	heats, err := h.DB.GetEventHeats(eventID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, heats)
}

// UpdateHeatStartTime handles updating the start time for a heat
func (h *Handler) UpdateHeatStartTime(c *gin.Context) {
	heatIDStr := c.Param("heat_id")
	heatID, err := strconv.ParseInt(heatIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid heat ID"})
		return
	}

	startTime := c.PostForm("start_time")
	if startTime == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Start time is required"})
		return
	}

	err = h.DB.UpdateHeatStartTime(heatID, startTime)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Start time updated successfully"})
}

// UpdateHeatFinishTime handles updating finish time for a participant in a specific heat
func (h *Handler) UpdateHeatFinishTime(c *gin.Context) {
	heatIDStr := c.Param("heat_id")
	participantIDStr := c.Param("participant_id")

	log.Printf("DEBUG: UpdateHeatFinishTime called with heat_id=%s, participant_id=%s", heatIDStr, participantIDStr)

	heatID, err := strconv.ParseInt(heatIDStr, 10, 64)
	if err != nil {
		log.Printf("ERROR: Invalid heat ID: %s", heatIDStr)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid heat ID"})
		return
	}

	participantID, err := strconv.ParseInt(participantIDStr, 10, 64)
	if err != nil {
		log.Printf("ERROR: Invalid participant ID: %s", participantIDStr)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid participant ID"})
		return
	}

	// Get form data
	finishTime := c.PostForm("finish_time_" + participantIDStr)
	dns := c.PostForm("dns_"+participantIDStr) == "on"
	dnf := c.PostForm("dnf_"+participantIDStr) == "on"

	log.Printf("DEBUG: Form data - finish_time_%s=%s, dns_%s=%v, dnf_%s=%v",
		participantIDStr, finishTime, participantIDStr, dns, participantIDStr, dnf)

	// Update the heat finish time
	err = h.DB.UpdateHeatFinishTime(heatID, participantID, finishTime, dns, dnf)
	if err != nil {
		log.Printf("ERROR: Failed to update heat finish time: %v", err)
		c.HTML(http.StatusInternalServerError, "finish_time_status.html", gin.H{
			"status":  "error",
			"message": err.Error(),
		})
		return
	}

	log.Printf("SUCCESS: Updated heat finish time for heat %d, participant %d", heatID, participantID)
	c.HTML(http.StatusOK, "finish_time_status.html", gin.H{
		"status":  "success",
		"message": "Måltid uppdaterad",
	})
}

// GetHeatResults handles displaying results for a specific heat
func (h *Handler) GetHeatResults(c *gin.Context) {
	idStr := c.Param("id")
	heatID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Invalid heat ID",
		})
		return
	}

	// Get heat results
	results, err := h.DB.GetHeatResultsComplete(heatID)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get the heat information
	heat, err := h.DB.GetHeat(heatID)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get the event
	event, err := h.DB.GetEvent(heat.EventID)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	c.HTML(http.StatusOK, "heat_results.html", gin.H{
		"title":      "Resultat - " + heat.Name,
		"event":      event,
		"heat":       heat,
		"results":    results,
		"use24hTime": h.getTimeFormatSetting(),
	})
}

// UpdateHeatPlacement handles updating placement for entypsegling events
func (h *Handler) UpdateHeatPlacement(c *gin.Context) {
	// Get heat ID from URL
	heatIDStr := c.Param("heat_id")
	heatID, err := strconv.ParseInt(heatIDStr, 10, 64)
	if err != nil {
		log.Printf("ERROR: Invalid heat ID: %s", heatIDStr)
		c.HTML(http.StatusBadRequest, "finish_time_status.html", gin.H{
			"status":  "error",
			"message": "Invalid heat ID",
		})
		return
	}

	// Get participant ID from URL
	participantIDStr := c.Param("participant_id")
	participantID, err := strconv.ParseInt(participantIDStr, 10, 64)
	if err != nil {
		log.Printf("ERROR: Invalid participant ID: %s", participantIDStr)
		c.HTML(http.StatusBadRequest, "finish_time_status.html", gin.H{
			"status":  "error",
			"message": "Invalid participant ID",
		})
		return
	}

	// Get placement from form
	placementStr := c.PostForm("placement_" + participantIDStr)
	dns := c.PostForm("dns_"+participantIDStr) == "on"
	dnf := c.PostForm("dnf_"+participantIDStr) == "on"

	log.Printf("DEBUG: Form data - placement_%s=%s, dns_%s=%v, dnf_%s=%v",
		participantIDStr, placementStr, participantIDStr, dns, participantIDStr, dnf)

	// Parse placement
	var placement int
	if placementStr != "" && !dns && !dnf {
		placement, err = strconv.Atoi(placementStr)
		if err != nil || placement < 1 {
			log.Printf("ERROR: Invalid placement: %s", placementStr)
			c.HTML(http.StatusBadRequest, "finish_time_status.html", gin.H{
				"status":  "error",
				"message": "Invalid placement - must be a positive number",
			})
			return
		}
	}

	// Update the heat placement
	err = h.DB.UpdateHeatPlacement(heatID, participantID, placement, dns, dnf)
	if err != nil {
		log.Printf("ERROR: Failed to update heat placement: %v", err)
		c.HTML(http.StatusInternalServerError, "finish_time_status.html", gin.H{
			"status":  "error",
			"message": err.Error(),
		})
		return
	}

	log.Printf("DEBUG: Successfully updated heat placement for participant %d in heat %d to %d", participantID, heatID, placement)

	// Return success message
	c.HTML(http.StatusOK, "finish_time_status.html", gin.H{
		"status": "success",
		"DNS":    dns,
		"DNF":    dnf,
	})
}
