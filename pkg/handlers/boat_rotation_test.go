package handlers

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rb<PERSON><PERSON>gren/segling/pkg/database"
)

// TestBoatRotation tests the boat rotation functionality
func TestBoatRotation(t *testing.T) {
	// Create test database
	db, err := database.New("test_boat_rotation.db")
	if err != nil {
		t.Fatalf("Failed to create test database: %v", err)
	}
	defer func() {
		db.Close()
		// Clean up test database
		// os.Remove("test_boat_rotation.db")
	}()

	// Initialize the database
	if err := db.Initialize(); err != nil {
		t.Fatalf("Failed to initialize test database: %v", err)
	}

	// Create handler
	handler := NewHandler(db)

	// Create test data
	eventID, participantIDs := createTestEventForRotation(t, db)

	t.Run("POST /events/:id/rotation - Enable Boat Rotation", func(t *testing.T) {
		testEnableBoatRotation(t, handler, eventID)
	})

	t.Run("GET /events/:id/rotation-matrix/print - Print Rotation Matrix", func(t *testing.T) {
		testPrintRotationMatrix(t, handler, eventID)
	})

	t.Run("Boat Rotation Algorithm", func(t *testing.T) {
		testBoatRotationAlgorithm(t, db, eventID, participantIDs)
	})

	t.Run("Heat Participant Filtering", func(t *testing.T) {
		testHeatParticipantFiltering(t, db, eventID, participantIDs)
	})
}

// testEnableBoatRotation tests enabling boat rotation for an event
func testEnableBoatRotation(t *testing.T, handler *Handler, eventID int64) {
	// Create multiple heats first
	_, err := handler.DB.CreateHeat(eventID, 2, "Heat 2", "13:00")
	if err != nil {
		t.Fatalf("Failed to create heat 2: %v", err)
	}

	_, err = handler.DB.CreateHeat(eventID, 3, "Heat 3", "14:00")
	if err != nil {
		t.Fatalf("Failed to create heat 3: %v", err)
	}

	// Test enabling rotation
	form := url.Values{}
	form.Add("rotation_enabled", "on")
	form.Add("boats_per_heat", "2")
	form.Add("races_per_boat", "2")

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", fmt.Sprintf("/events/%d/rotation", eventID), strings.NewReader(form.Encode()))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	router := gin.New()
	router.POST("/events/:id/rotation", handler.UpdateEventRotation)

	router.ServeHTTP(w, req)

	// Should redirect back to heats page
	if w.Code != http.StatusSeeOther {
		t.Errorf("Expected status %d, got %d", http.StatusSeeOther, w.Code)
	}

	// Verify the event was updated
	event, err := handler.DB.GetEvent(eventID)
	if err != nil {
		t.Fatalf("Failed to get updated event: %v", err)
	}

	if !event.RotationEnabled {
		t.Error("Expected rotation to be enabled")
	}

	if event.BoatsPerHeat != 2 {
		t.Errorf("Expected boats per heat to be 2, got %d", event.BoatsPerHeat)
	}

	if event.RacesPerBoat != 2 {
		t.Errorf("Expected races per boat to be 2, got %d", event.RacesPerBoat)
	}
}

// testPrintRotationMatrix tests the rotation matrix print functionality
func testPrintRotationMatrix(t *testing.T, handler *Handler, eventID int64) {
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", fmt.Sprintf("/events/%d/rotation-matrix/print", eventID), nil)

	router := gin.New()
	router.GET("/events/:id/rotation-matrix/print", func(c *gin.Context) {
		// Call the handler but catch any template errors
		defer func() {
			if r := recover(); r != nil {
				c.String(http.StatusOK, "Rotation matrix would render here")
			}
		}()
		handler.PrintRotationMatrix(c)
	})

	router.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}
}

// testBoatRotationAlgorithm tests the rotation algorithm
func testBoatRotationAlgorithm(t *testing.T, db *database.DB, eventID int64, participantIDs []int64) {
	// Test the rotation generation
	err := db.GenerateBoatRotation(eventID, 2, 2)
	if err != nil {
		t.Fatalf("Failed to generate boat rotation: %v", err)
	}

	// Get the rotation matrix
	participants, heats, matrix, err := db.GetEventRotationMatrix(eventID)
	if err != nil {
		t.Fatalf("Failed to get rotation matrix: %v", err)
	}

	// Verify we have participants and heats
	if len(participants) == 0 {
		t.Error("Expected participants in rotation matrix")
	}

	if len(heats) == 0 {
		t.Error("Expected heats in rotation matrix")
	}

	if len(matrix) != len(participants) {
		t.Errorf("Expected matrix rows (%d) to match participants (%d)", len(matrix), len(participants))
	}

	// Verify each participant is assigned to some heats
	for i, participant := range participants {
		participantHeats := 0
		for j := range heats {
			if matrix[i][j] {
				participantHeats++
			}
		}

		if participantHeats == 0 {
			t.Errorf("Participant %d is not assigned to any heats", participant.ID)
		}

		t.Logf("Participant %d is assigned to %d heats", participant.ID, participantHeats)
	}
}

// testHeatParticipantFiltering tests that participants are filtered by heat when rotation is enabled
func testHeatParticipantFiltering(t *testing.T, db *database.DB, eventID int64, participantIDs []int64) {
	// Get all heats for the event
	heats, err := db.GetEventHeats(eventID)
	if err != nil {
		t.Fatalf("Failed to get event heats: %v", err)
	}

	if len(heats) < 2 {
		t.Skip("Need at least 2 heats for filtering test")
	}

	// Test getting participants for a specific heat
	heat1Participants, err := db.GetHeatParticipantsForEvent(eventID, heats[0].ID)
	if err != nil {
		t.Fatalf("Failed to get heat 1 participants: %v", err)
	}

	heat2Participants, err := db.GetHeatParticipantsForEvent(eventID, heats[1].ID)
	if err != nil {
		t.Fatalf("Failed to get heat 2 participants: %v", err)
	}

	// With rotation enabled and boats_per_heat=2, each heat should have at most 2 participants
	if len(heat1Participants) > 2 {
		t.Errorf("Heat 1 has %d participants, expected at most 2", len(heat1Participants))
	}

	if len(heat2Participants) > 2 {
		t.Errorf("Heat 2 has %d participants, expected at most 2", len(heat2Participants))
	}

	t.Logf("Heat 1 has %d participants, Heat 2 has %d participants", len(heat1Participants), len(heat2Participants))

	// Test getting all participants (when heat ID is 0)
	allParticipants, err := db.GetHeatParticipantsForEvent(eventID, 0)
	if err != nil {
		t.Fatalf("Failed to get all participants: %v", err)
	}

	// Should return all participants when heat ID is 0
	if len(allParticipants) != len(participantIDs) {
		t.Errorf("Expected %d participants when heat ID is 0, got %d", len(participantIDs), len(allParticipants))
	}
}

// createTestEventForRotation creates a test event with multiple participants for rotation testing
func createTestEventForRotation(t *testing.T, db *database.DB) (int64, []int64) {
	// Create test event
	eventDate, _ := time.Parse("2006-01-02", "2024-01-15")
	eventID, err := db.CreateEvent("Test Rotation Event", eventDate, "10:00", 5, 1000, false, "Test", "Test rotation event")
	if err != nil {
		t.Fatalf("Failed to create test event: %v", err)
	}

	// Create test sailors and boats
	var participantIDs []int64
	for i := 1; i <= 6; i++ { // Create 6 participants for rotation testing
		// Create sailor
		sailorID, err := db.CreateSailor(fmt.Sprintf("Test Sailor %d", i), "123456789", "LSS")
		if err != nil {
			t.Fatalf("Failed to create sailor %d: %v", i, err)
		}

		// Create boat
		boatID, err := db.CreateBoat(fmt.Sprintf("Test Boat %d", i), "Test Type", "MAT123", fmt.Sprintf("%d", i), "SWE", 1.0, 0.95, 1.05, 1.0)
		if err != nil {
			t.Fatalf("Failed to create boat %d: %v", i, err)
		}

		// Create participant
		participantID, err := db.AddParticipantToEvent(eventID, sailorID, boatID, "srs", 1.0, 0.0, false)
		if err != nil {
			t.Fatalf("Failed to create participant %d: %v", i, err)
		}

		participantIDs = append(participantIDs, participantID)
	}

	return eventID, participantIDs
}
