package handlers

import (
	"encoding/csv"
	"fmt"
	"log"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rbjoregren/segling/pkg/models"
	"github.com/rbjoregren/segling/pkg/utils"
)

// GetFinishTimes renders the finish times page
func (h *Handler) GetFinishTimes(c *gin.Context) {
	// Get all events for the dropdown (do this first to ensure we have events for the template)
	allEvents, err := h.DB.GetEvents()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Error fetching events: " + err.Error(),
		})
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		// If the ID is invalid, redirect to the events page
		c.Redirect(http.StatusSeeOther, "/events")
		return
	}

	event, err := h.DB.GetEvent(id)
	if err != nil {
		// If the event doesn't exist, show a more user-friendly error
		c.HTML(http.StatusOK, "events.html", gin.H{
			"title":                  "Seglingstävlingar",
			"events":                 allEvents,
			"allEvents":              allEvents,
			"activeMenu":             "list",
			"error":                  "Tävlingen kunde inte hittas. Välj en annan tävling.",
			"selectedEventJaktstart": false,
		})
		return
	}

	// Ensure the event has at least one heat
	err = h.DB.EnsureDefaultHeat(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get heats for this event
	heats, err := h.DB.GetEventHeats(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get selected heat from query parameter, default to first heat
	selectedHeatIDStr := c.Query("heat")
	var selectedHeat models.Heat
	if selectedHeatIDStr != "" {
		selectedHeatID, err := strconv.ParseInt(selectedHeatIDStr, 10, 64)
		if err == nil {
			for _, heat := range heats {
				if heat.ID == selectedHeatID {
					selectedHeat = heat
					break
				}
			}
		}
	}
	// If no heat selected or invalid heat ID, use first heat
	if selectedHeat.ID == 0 && len(heats) > 0 {
		selectedHeat = heats[0]
	}

	// Debug logging
	log.Printf("DEBUG: Event %d has %d heats, selected heat ID: %d", id, len(heats), selectedHeat.ID)

	// Get participants for this event (filtered by heat if rotation is enabled)
	participants, err := h.DB.GetHeatParticipantsForEvent(id, selectedHeat.ID)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get finish times for the selected heat
	var heatFinishTimes []models.HeatFinishTime
	if selectedHeat.ID != 0 {
		heatFinishTimes, err = h.DB.GetHeatFinishTimes(selectedHeat.ID)
		if err != nil {
			c.HTML(http.StatusInternalServerError, "error.html", gin.H{
				"error": err.Error(),
			})
			return
		}
	}

	// Create a map for quick lookup of heat finish times
	heatFinishTimeMap := make(map[int64]models.HeatFinishTime)
	for _, ft := range heatFinishTimes {
		heatFinishTimeMap[ft.ParticipantID] = ft
	}

	// Enrich participants with sailor and boat information
	var enrichedParticipants []ParticipantWithStartTime

	// If jaktstart is enabled, calculate start times
	if event.IsJaktstart() {
		// Use heat-specific start time if available, otherwise use event start time
		var baseStartTimeStr string
		if selectedHeat.StartTime != "" {
			baseStartTimeStr = selectedHeat.StartTime
		} else {
			baseStartTimeStr = event.Starttid
		}

		// Parse the base start time
		baseStartTime, err := time.Parse("15:04", baseStartTimeStr)
		if err != nil {
			// If the start time is invalid, use noon as the base
			baseStartTime = time.Date(event.Datum.Year(), event.Datum.Month(), event.Datum.Day(), 12, 0, 0, 0, event.Datum.Location())
		}

		// First, find the lowest SRS value among all participants
		lowestSRS := 9999.0 // Start with a high value
		var validParticipants []struct {
			Participant models.EventParticipant
			Sailor      models.Sailor
			Boat        models.Boat
			SRSValue    float64
		}

		for _, p := range participants {
			sailor, err := h.DB.GetSailor(p.SailorID)
			if err != nil {
				continue
			}

			boat, err := h.DB.GetBoat(p.BoatID)
			if err != nil {
				continue
			}

			// Get the SRS value to use
			var srsValue float64
			if p.UseCustomSRSValue && p.CustomSRSValue > 0 {
				// If using a custom SRS value, use it
				srsValue = p.CustomSRSValue
			} else {
				// Always use the boat's SRS value based on the SRS type
				switch p.SRSType {
				case "srs_utan_undanvindsegel":
					srsValue = boat.SRSUtanUndanvindsegel
				case "srs_shorthanded":
					srsValue = boat.SRSShorthanded
				case "srs_shorthanded_utan_undanvindsegel":
					srsValue = boat.SRSShorthandedUtanUndanvindsegel
				default:
					srsValue = boat.SRS
				}
			}

			// Skip invalid SRS values
			if srsValue <= 0 {
				continue
			}

			// Update lowest SRS if needed
			if srsValue < lowestSRS {
				lowestSRS = srsValue
			}

			validParticipants = append(validParticipants, struct {
				Participant models.EventParticipant
				Sailor      models.Sailor
				Boat        models.Boat
				SRSValue    float64
			}{
				Participant: p,
				Sailor:      sailor,
				Boat:        boat,
				SRSValue:    srsValue,
			})
		}

		// Calculate start times for each participant
		for _, vp := range validParticipants {
			// Calculate the start time offset in seconds
			offsetSeconds := utils.CalculateStartTime(float64(event.Banlangd), event.Vind, vp.SRSValue, lowestSRS)

			// Format the offset as a human-readable string
			offsetStr := utils.FormatStartTime(offsetSeconds)

			// Calculate the absolute start time
			absoluteStartTime := utils.CalculateAbsoluteStartTime(baseStartTime, offsetSeconds)
			absoluteStartTimeStr := utils.FormatAbsoluteStartTime(absoluteStartTime)

			// Add to the list
			enrichedParticipants = append(enrichedParticipants, ParticipantWithStartTime{
				EventParticipant:  vp.Participant,
				StartTimeOffset:   offsetStr,
				AbsoluteStartTime: absoluteStartTimeStr,
				Sailor:            vp.Sailor,
				Boat:              vp.Boat,
			})
		}

		// Sort participants by start time (earliest first)
		sort.Slice(enrichedParticipants, func(i, j int) bool {
			timeI, _ := time.Parse("15:04", enrichedParticipants[i].AbsoluteStartTime)
			timeJ, _ := time.Parse("15:04", enrichedParticipants[j].AbsoluteStartTime)
			return timeI.Before(timeJ)
		})
	} else {
		// Regular race, no jaktstart
		for _, p := range participants {
			sailor, err := h.DB.GetSailor(p.SailorID)
			if err != nil {
				continue
			}

			var boat models.Boat
			// For entypsegling events, participants don't have boats
			if !event.Entypsegling {
				boat, err = h.DB.GetBoat(p.BoatID)
				if err != nil {
					continue
				}
			}

			// Update the participant's SRS value for display
			updatedParticipant := p

			// For entypsegling events, we don't need SRS calculations
			if !event.Entypsegling {
				// Get the SRS value to use for display
				var srsValue float64
				if p.UseCustomSRSValue && p.CustomSRSValue > 0 {
					// If using a custom SRS value, use it
					srsValue = p.CustomSRSValue
				} else {
					// Always use the boat's SRS value based on the SRS type
					switch p.SRSType {
					case "srs_utan_undanvindsegel":
						srsValue = boat.SRSUtanUndanvindsegel
					case "srs_shorthanded":
						srsValue = boat.SRSShorthanded
					case "srs_shorthanded_utan_undanvindsegel":
						srsValue = boat.SRSShorthandedUtanUndanvindsegel
					default:
						srsValue = boat.SRS
					}
				}

				// If we're using a boat's SRS value, make sure we're using the correct SRS type
				if updatedParticipant.SelectedSRSValue <= 0 {
					updatedParticipant.SelectedSRSValue = srsValue

					// If SRS type is empty or default, set it based on the boat's SRS value we're using
					if updatedParticipant.SRSType == "" || updatedParticipant.SRSType == "srs" {
						// Determine which SRS value we're using from the boat
						switch srsValue {
						case boat.SRSUtanUndanvindsegel:
							updatedParticipant.SRSType = "srs_utan_undanvindsegel"
						case boat.SRSShorthanded:
							updatedParticipant.SRSType = "srs_shorthanded"
						case boat.SRSShorthandedUtanUndanvindsegel:
							updatedParticipant.SRSType = "srs_shorthanded_utan_undanvindsegel"
						default:
							updatedParticipant.SRSType = "srs"
						}
					}
				}
			}

			enrichedParticipants = append(enrichedParticipants, ParticipantWithStartTime{
				EventParticipant: updatedParticipant,
				Sailor:           sailor,
				Boat:             boat,
			})
		}
	}

	c.HTML(http.StatusOK, "finish_times.html", gin.H{
		"title":                  "Måltider - " + event.Namn,
		"event":                  event,
		"participants":           enrichedParticipants,
		"allEvents":              allEvents,
		"selectedEventID":        id,
		"selectedEventJaktstart": event.Jaktstart,
		"activeMenu":             "finish",
		"use24hTime":             h.getTimeFormatSetting(),
		"heats":                  heats,
		"selectedHeat":           selectedHeat,
		"heatFinishTimeMap":      heatFinishTimeMap,
	})
}

// UpdateFinishTime updates the finish time for a participant
func (h *Handler) UpdateFinishTime(c *gin.Context) {
	// Log the request parameters for debugging
	eventIDStr := c.Param("event_id")
	participantIDStr := c.Param("participant_id")
	finishTimeParam := c.PostForm("finish_time_" + participantIDStr)

	// Also check for event ID in headers (as a backup)
	headerEventID := c.GetHeader("X-Event-ID")

	// Log all parameters for debugging
	utils.LogDebug("UpdateFinishTime called with event_id:", eventIDStr,
		"participant_id:", participantIDStr,
		"finish_time param:", finishTimeParam,
		"header event_id:", headerEventID)

	// Validate event ID
	var eventID int64
	var err error

	// Try to parse the event ID from the URL parameter
	eventID, err = strconv.ParseInt(eventIDStr, 10, 64)
	if err != nil {
		// If that fails, try the header
		utils.LogWarning("Invalid event ID in URL parameter:", eventIDStr, "Error:", err, "Trying header...")

		if headerEventID != "" {
			eventID, err = strconv.ParseInt(headerEventID, 10, 64)
			if err != nil {
				utils.LogError("Invalid event ID in both URL and header. URL:", eventIDStr, "Header:", headerEventID)
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event ID"})
				return
			}
		} else {
			utils.LogError("Invalid event ID:", eventIDStr, "and no header event ID provided")
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event ID"})
			return
		}
	}

	// Verify the event exists
	_, err = h.DB.GetEvent(eventID)
	if err != nil {
		utils.LogError("Event not found:", eventID, "Error:", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Event not found"})
		return
	}

	// Validate participant ID
	participantID, err := strconv.ParseInt(participantIDStr, 10, 64)
	if err != nil {
		utils.LogError("Invalid participant ID:", participantIDStr, "Error:", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid participant ID"})
		return
	}

	// Get the participant to verify it exists and belongs to this event
	participant, err := h.DB.GetParticipant(participantID)
	if err != nil {
		utils.LogError("Participant not found:", participantID, "Error:", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Participant not found"})
		return
	}

	// Verify the participant belongs to this event
	if participant.EventID != eventID {
		utils.LogError("Participant", participantID, "does not belong to event", eventID,
			"but to event", participant.EventID)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Participant does not belong to this event"})
		return
	}

	// Get the finish time from the form
	finishTime := c.PostForm("finish_time_" + participantIDStr)

	// If no finish time is provided, automatically set DNS
	if finishTime == "" {
		utils.LogDebug("No finish time provided for participant:", participantID, "- setting DNS")

		// Set DNS status
		err = h.DB.UpdateParticipantDNS(participantID, true)
		if err != nil {
			utils.LogError("Error setting DNS status:", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		utils.LogDebug("Successfully set DNS for participant:", participantID)

		// Return success message with DNS status
		c.HTML(http.StatusOK, "finish_time_status.html", gin.H{
			"status": "success",
			"DNS":    true,
			"DNF":    false,
		})
		return
	}

	// Update the finish time (this will clear DNS and DNF flags)
	err = h.DB.UpdateParticipantFinishTime(participantID, finishTime)
	if err != nil {
		utils.LogError("Error updating finish time:", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	utils.LogDebug("Successfully updated finish time for participant:", participantID, "to", finishTime)

	// Return success message
	c.HTML(http.StatusOK, "finish_time_status.html", gin.H{
		"status": "success",
		"DNS":    false,
		"DNF":    false,
	})
}

// UpdateParticipantDNS updates the DNS (Did Not Start) status for a participant
func (h *Handler) UpdateParticipantDNS(c *gin.Context) {
	// Log the request parameters for debugging
	eventIDStr := c.Param("id")
	participantIDStr := c.Param("participant_id")
	dnsValue := c.PostForm("dns_" + participantIDStr)

	// Also check for event ID in headers (as a backup)
	headerEventID := c.GetHeader("X-Event-ID")

	// Log all parameters for debugging
	utils.LogDebug("UpdateParticipantDNS called with event_id:", eventIDStr,
		"participant_id:", participantIDStr,
		"dns value:", dnsValue,
		"header event_id:", headerEventID)

	// Validate event ID
	var eventID int64
	var err error

	// Try to parse the event ID from the URL parameter
	eventID, err = strconv.ParseInt(eventIDStr, 10, 64)
	if err != nil {
		// If that fails, try the header
		utils.LogWarning("Invalid event ID in URL parameter:", eventIDStr, "Error:", err, "Trying header...")

		if headerEventID != "" {
			eventID, err = strconv.ParseInt(headerEventID, 10, 64)
			if err != nil {
				utils.LogError("Invalid event ID in both URL and header. URL:", eventIDStr, "Header:", headerEventID)
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event ID"})
				return
			}
		} else {
			utils.LogError("Invalid event ID:", eventIDStr, "and no header event ID provided")
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event ID"})
			return
		}
	}

	// Verify the event exists
	_, err = h.DB.GetEvent(eventID)
	if err != nil {
		utils.LogError("Event not found:", eventID, "Error:", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Event not found"})
		return
	}

	// Validate participant ID
	participantID, err := strconv.ParseInt(participantIDStr, 10, 64)
	if err != nil {
		utils.LogError("Invalid participant ID:", participantIDStr, "Error:", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid participant ID"})
		return
	}

	// Get the participant to verify it exists and belongs to this event
	participant, err := h.DB.GetParticipant(participantID)
	if err != nil {
		utils.LogError("Participant not found:", participantID, "Error:", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Participant not found"})
		return
	}

	// Verify the participant belongs to this event
	if participant.EventID != eventID {
		utils.LogError("Participant", participantID, "does not belong to event", eventID,
			"but to event", participant.EventID)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Participant does not belong to this event"})
		return
	}

	// Determine the DNS value (checkbox is "on" when checked)
	dns := dnsValue == "on" || dnsValue == "true"

	// Update the DNS status
	err = h.DB.UpdateParticipantDNS(participantID, dns)
	if err != nil {
		utils.LogError("Error updating DNS status:", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	utils.LogDebug("Successfully updated DNS status for participant:", participantID, "to", dns)

	// Get the updated participant to show the correct status
	updatedParticipant, err := h.DB.GetParticipant(participantID)
	if err != nil {
		utils.LogError("Error getting updated participant:", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return success message with the updated status
	c.HTML(http.StatusOK, "finish_time_status.html", gin.H{
		"status": "success",
		"DNS":    updatedParticipant.DNS,
		"DNF":    updatedParticipant.DNF,
	})
}

// UpdateParticipantDNF updates the DNF (Did Not Finish) status for a participant
func (h *Handler) UpdateParticipantDNF(c *gin.Context) {
	// Log the request parameters for debugging
	eventIDStr := c.Param("id")
	participantIDStr := c.Param("participant_id")
	dnfValue := c.PostForm("dnf_" + participantIDStr)

	// Also check for event ID in headers (as a backup)
	headerEventID := c.GetHeader("X-Event-ID")

	// Log all parameters for debugging
	utils.LogDebug("UpdateParticipantDNF called with event_id:", eventIDStr,
		"participant_id:", participantIDStr,
		"dnf value:", dnfValue,
		"header event_id:", headerEventID)

	// Validate event ID
	var eventID int64
	var err error

	// Try to parse the event ID from the URL parameter
	eventID, err = strconv.ParseInt(eventIDStr, 10, 64)
	if err != nil {
		// If that fails, try the header
		utils.LogWarning("Invalid event ID in URL parameter:", eventIDStr, "Error:", err, "Trying header...")

		if headerEventID != "" {
			eventID, err = strconv.ParseInt(headerEventID, 10, 64)
			if err != nil {
				utils.LogError("Invalid event ID in both URL and header. URL:", eventIDStr, "Header:", headerEventID)
				c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event ID"})
				return
			}
		} else {
			utils.LogError("Invalid event ID:", eventIDStr, "and no header event ID provided")
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid event ID"})
			return
		}
	}

	// Verify the event exists
	_, err = h.DB.GetEvent(eventID)
	if err != nil {
		utils.LogError("Event not found:", eventID, "Error:", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Event not found"})
		return
	}

	// Validate participant ID
	participantID, err := strconv.ParseInt(participantIDStr, 10, 64)
	if err != nil {
		utils.LogError("Invalid participant ID:", participantIDStr, "Error:", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid participant ID"})
		return
	}

	// Get the participant to verify it exists and belongs to this event
	participant, err := h.DB.GetParticipant(participantID)
	if err != nil {
		utils.LogError("Participant not found:", participantID, "Error:", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Participant not found"})
		return
	}

	// Verify the participant belongs to this event
	if participant.EventID != eventID {
		utils.LogError("Participant", participantID, "does not belong to event", eventID,
			"but to event", participant.EventID)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Participant does not belong to this event"})
		return
	}

	// Determine the DNF value (checkbox is "on" when checked)
	dnf := dnfValue == "on" || dnfValue == "true"

	// Update the DNF status
	err = h.DB.UpdateParticipantDNF(participantID, dnf)
	if err != nil {
		utils.LogError("Error updating DNF status:", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	utils.LogDebug("Successfully updated DNF status for participant:", participantID, "to", dnf)

	// Get the updated participant to show the correct status
	updatedParticipant, err := h.DB.GetParticipant(participantID)
	if err != nil {
		utils.LogError("Error getting updated participant:", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return success message with the updated status
	c.HTML(http.StatusOK, "finish_time_status.html", gin.H{
		"status": "success",
		"DNS":    updatedParticipant.DNS,
		"DNF":    updatedParticipant.DNF,
	})
}

// GetResults renders the results page
func (h *Handler) GetResults(c *gin.Context) {
	// Get all events for the dropdown (do this first to ensure we have events for the template)
	allEvents, err := h.DB.GetEvents()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Error fetching events: " + err.Error(),
		})
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		// If the ID is invalid, redirect to the events page
		c.Redirect(http.StatusSeeOther, "/events")
		return
	}

	event, err := h.DB.GetEvent(id)
	if err != nil {
		// If the event doesn't exist, show a more user-friendly error
		c.HTML(http.StatusOK, "events.html", gin.H{
			"title":                  "Seglingstävlingar",
			"events":                 allEvents,
			"allEvents":              allEvents,
			"activeMenu":             "list",
			"error":                  "Tävlingen kunde inte hittas. Välj en annan tävling.",
			"selectedEventJaktstart": false,
		})
		return
	}

	// Variable to store results
	var results []models.Result
	var savedResults []models.SavedResult
	var usingSavedResults bool

	// Get heats for this event to determine if it's single or multi-heat
	heats, err := h.DB.GetEventHeats(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Ensure default heat exists
	err = h.DB.EnsureDefaultHeat(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get heats again after ensuring default
	heats, err = h.DB.GetEventHeats(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	var totalResults []models.TotalResult
	var isMultiHeat bool = len(heats) > 1

	// If the event is locked, get the saved results
	if event.Locked {
		log.Printf("GetResults: Event is locked, getting saved results")
		savedResults, err = h.DB.GetSavedResults(id)
		if err != nil {
			log.Printf("GetResults: Error getting saved results: %v", err)
			// If there's an error getting saved results, fall back to calculating them
			if isMultiHeat {
				totalResults, err = h.DB.GetEventTotalResults(id)
				if err != nil {
					c.HTML(http.StatusInternalServerError, "error.html", gin.H{
						"error": err.Error(),
					})
					return
				}
			} else {
				results, err = h.DB.GetEventResults(id)
				if err != nil {
					c.HTML(http.StatusInternalServerError, "error.html", gin.H{
						"error": err.Error(),
					})
					return
				}
			}
		} else {
			usingSavedResults = true
			log.Printf("GetResults: Found %d saved results for event", len(savedResults))
		}
	} else {
		// If the event is not locked, calculate the results
		log.Printf("GetResults: Event is not locked, calculating results")
		if isMultiHeat {
			log.Printf("GetResults: Multi-heat event, calculating total results")
			totalResults, err = h.DB.GetEventTotalResults(id)
			if err != nil {
				c.HTML(http.StatusInternalServerError, "error.html", gin.H{
					"error": err.Error(),
				})
				return
			}
			log.Printf("GetResults: Found %d total results for multi-heat event", len(totalResults))
		} else {
			log.Printf("GetResults: Single heat event, calculating regular results")
			// For single heat events, use the heat-based results but convert to regular Result format
			if len(heats) > 0 {
				heatResults, err := h.DB.GetHeatResultsComplete(heats[0].ID)
				if err != nil {
					c.HTML(http.StatusInternalServerError, "error.html", gin.H{
						"error": err.Error(),
					})
					return
				}
				// Convert heat results to regular results format
				results = h.convertHeatResultsToResults(heatResults)
				log.Printf("GetResults: Found %d results for single heat event", len(results))
			} else {
				log.Printf("GetResults: No heats found for single heat event")
				results = []models.Result{}
			}
		}
	}

	// Check if GitHub Pages integration is enabled
	githubPagesEnabled, err := h.DB.GetSetting("github_pages_enabled")
	if err != nil {
		githubPagesEnabled = "false" // Default to disabled if setting not found
	}

	// Check if Google Drive integration is enabled
	googleDriveEnabled, err := h.DB.GetSetting("google_drive_enabled")
	if err != nil {
		googleDriveEnabled = "false" // Default to disabled if setting not found
	}

	// Check if the event has been published
	isPublished := false
	var publishedFilename string
	var publishedUrl string

	if githubPagesEnabled == "true" {
		// Generate the expected filename for this event
		baseFilename := fmt.Sprintf("%s-%s.html", event.Datum.Format("2006-01-02"), strings.ReplaceAll(strings.ToLower(event.Namn), " ", "-"))

		// Get the year from the event date
		eventYear := event.Datum.Format("2006")

		// Get the competition type, default to "Kvällssegling" if empty
		competitionType := event.Tavlingstyp
		if competitionType == "" {
			competitionType = "Kvällssegling"
		}

		// Create the full path with year and competition type directories
		fullPath := fmt.Sprintf("%s/%s/%s", eventYear, competitionType, baseFilename)

		// Store the full path for deletion purposes
		publishedFilename = fullPath

		// Get GitHub Pages settings
		githubPagesRepo, err := h.DB.GetSetting("github_pages_repo")
		githubPagesBranch, err2 := h.DB.GetSetting("github_pages_branch")
		githubPagesToken, err3 := h.DB.GetSetting("github_pages_token")

		if err == nil && githubPagesRepo != "" && err2 == nil && githubPagesBranch != "" && err3 == nil && githubPagesToken != "" {
			// Generate the URL to the published page
			publishedUrl = fmt.Sprintf("https://%s.github.io/%s/%s",
				strings.Split(githubPagesRepo, "/")[0],
				strings.Split(githubPagesRepo, "/")[1],
				fullPath)

			// Check if the file actually exists on GitHub using the full path
			log.Printf("Checking if file exists at path: %s", fullPath)
			exists, checkErr := h.checkFileExists(githubPagesRepo, githubPagesBranch, githubPagesToken, fullPath)
			if checkErr != nil {
				log.Printf("Warning: Could not check if file exists: %v", checkErr)
				// If we can't check, assume it doesn't exist
				isPublished = false
			} else {
				isPublished = exists
			}
		}
	}

	// Prepare the template data
	templateData := gin.H{
		"title":                  "Resultat - " + event.Namn,
		"event":                  event,
		"heats":                  heats,
		"isMultiHeat":            isMultiHeat,
		"allEvents":              allEvents,
		"selectedEventID":        id,
		"selectedEventJaktstart": event.Jaktstart,
		"activeMenu":             "results",
		"add":                    func(i, j int) int { return i + j }, // Helper function for templates
		"use24hTime":             h.getTimeFormatSetting(),
		"githubPagesEnabled":     githubPagesEnabled == "true",
		"googleDriveEnabled":     googleDriveEnabled == "true",
		"isPublished":            isPublished,
		"publishedFilename":      publishedFilename,
		"publishedUrl":           publishedUrl,
		"usingSavedResults":      usingSavedResults,
	}

	// Add the appropriate results data based on single vs multi-heat
	if isMultiHeat {
		templateData["totalResults"] = totalResults

		// For multi-heat events, also provide individual heat results for tabs
		heatResultsMap := make(map[int64][]models.HeatResult)
		for _, heat := range heats {
			heatResults, err := h.DB.GetHeatResultsComplete(heat.ID)
			if err != nil {
				log.Printf("Error getting results for heat %d: %v", heat.ID, err)
				continue
			}
			heatResultsMap[heat.ID] = heatResults
		}
		templateData["heatResultsMap"] = heatResultsMap
	} else {
		// Add either results or savedResults to the template data for single heat
		if usingSavedResults {
			templateData["savedResults"] = savedResults
		} else {
			templateData["results"] = results
		}
	}

	c.HTML(http.StatusOK, "results.html", templateData)
}

// convertHeatResultsToResults converts heat results to regular results format for single heat events
func (h *Handler) convertHeatResultsToResults(heatResults []models.HeatResult) []models.Result {
	var results []models.Result

	for _, hr := range heatResults {
		// Create a copy of the EventParticipant and set the finish time from the heat result
		eventParticipant := hr.EventParticipant
		eventParticipant.FinishTime = hr.FinishTime
		eventParticipant.DNS = hr.DNS
		eventParticipant.DNF = hr.DNF

		result := models.Result{
			EventParticipant:      eventParticipant,
			Sailor:                hr.Sailor,
			Boat:                  hr.Boat,
			StartTime:             hr.StartTime,
			ElapsedTime:           hr.ElapsedTime,
			CorrectedTime:         hr.CorrectedTime,
			ElapsedSeconds:        hr.ElapsedSeconds,
			CorrectedSeconds:      hr.CorrectedSeconds,
			CorrectedSecondsFloat: hr.CorrectedSecondsFloat,
			TimeToPrevious:        hr.TimeToPrevious,
			TimeToWinner:          hr.TimeToWinner,
			TotalPersons:          hr.TotalPersons,
			DNS:                   hr.DNS,
			DNF:                   hr.DNF,
		}
		results = append(results, result)
	}

	return results
}

// GetStarterBoat handles the starter boat information page
func (h *Handler) GetStarterBoat(c *gin.Context) {
	// Simply redirect to the print version
	idStr := c.Param("id")
	c.Redirect(http.StatusSeeOther, "/events/"+idStr+"/starter-boat/print")
}

// GetStarterBoatPrint renders the print-friendly starter boat information page
func (h *Handler) GetStarterBoatPrint(c *gin.Context) {
	// Get all events for the dropdown (do this first to ensure we have events for the template)
	allEvents, err := h.DB.GetEvents()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Error fetching events: " + err.Error(),
		})
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		// If the ID is invalid, redirect to the events page
		c.Redirect(http.StatusSeeOther, "/events")
		return
	}

	log.Printf("GetStarterBoatPrint: Fetching event with ID %d", id)
	event, err := h.DB.GetEvent(id)
	if err != nil {
		// If the event doesn't exist, show a more user-friendly error
		c.HTML(http.StatusOK, "events.html", gin.H{
			"title":                  "Seglingstävlingar",
			"events":                 allEvents,
			"allEvents":              allEvents,
			"activeMenu":             "list",
			"error":                  "Tävlingen kunde inte hittas. Välj en annan tävling.",
			"selectedEventJaktstart": false,
		})
		return
	}
	log.Printf("GetStarterBoatPrint: Found event: %s (ID: %d)", event.Namn, event.ID)

	// Variable to store enriched participants
	var enrichedParticipants []ParticipantWithStartTime
	var usingSavedResults bool

	// If the event is locked, use saved results
	if event.Locked {
		log.Printf("GetStarterBoatPrint: Event is locked, using saved results")
		savedResults, err := h.DB.GetSavedResults(id)
		if err != nil {
			log.Printf("GetStarterBoatPrint: Error getting saved results: %v", err)
			// If there's an error getting saved results, fall back to live data
		} else if len(savedResults) > 0 {
			usingSavedResults = true
			log.Printf("GetStarterBoatPrint: Found %d saved results for event", len(savedResults))

			// Convert saved results to ParticipantWithStartTime format
			enrichedParticipants = h.convertSavedResultsToParticipants(savedResults, event)
		}
	}

	// If not using saved results (either event not locked or no saved results found), use live data
	if !usingSavedResults {
		log.Printf("GetStarterBoatPrint: Using live data for event")
		// Get participants for this event with their sailor and boat information
		participants, err := h.DB.GetEventParticipants(id)
		if err != nil {
			c.HTML(http.StatusInternalServerError, "error.html", gin.H{
				"error": err.Error(),
			})
			return
		}
		log.Printf("GetStarterBoatPrint: Found %d participants for event", len(participants))

		// If jaktstart is enabled, calculate start times
		if event.IsJaktstart() {
			log.Printf("GetStarterBoatPrint: Event has jaktstart enabled")
			// Parse the base start time
			baseStartTime, err := time.Parse("15:04", event.Starttid)
			if err != nil {
				// If the start time is invalid, use noon as the base
				baseStartTime = time.Date(event.Datum.Year(), event.Datum.Month(), event.Datum.Day(), 12, 0, 0, 0, event.Datum.Location())
				log.Printf("GetStarterBoatPrint: Invalid start time format, using noon as default")
			} else {
				log.Printf("GetStarterBoatPrint: Base start time: %s", baseStartTime.Format("15:04"))
			}

			// First, find the lowest SRS value among all participants
			lowestSRS := 9999.0 // Start with a high value
			var validParticipants []struct {
				Participant models.EventParticipant
				Sailor      models.Sailor
				Boat        models.Boat
				SRSValue    float64
			}

			for i, p := range participants {
				log.Printf("GetStarterBoatPrint: Processing participant %d (ID: %d)", i+1, p.ID)
				sailor, err := h.DB.GetSailor(p.SailorID)
				if err != nil {
					log.Printf("GetStarterBoatPrint: Error fetching sailor %d: %v", p.SailorID, err)
					continue
				}
				log.Printf("GetStarterBoatPrint: Found sailor: %s (ID: %d)", sailor.Namn, sailor.ID)

				boat, err := h.DB.GetBoat(p.BoatID)
				if err != nil {
					log.Printf("GetStarterBoatPrint: Error fetching boat %d: %v", p.BoatID, err)
					continue
				}
				log.Printf("GetStarterBoatPrint: Found boat: %s (ID: %d, Type: %s)", boat.Namn, boat.ID, boat.Battyp)

				// Get the SRS value to use
				var srsValue float64
				if p.UseCustomSRSValue && p.CustomSRSValue > 0 {
					// If using a custom SRS value, use it
					srsValue = p.CustomSRSValue
					log.Printf("GetStarterBoatPrint: Using custom SRS value: %f", srsValue)
				} else {
					// Always use the boat's SRS value based on the SRS type
					switch p.SRSType {
					case "srs_utan_undanvindsegel":
						srsValue = boat.SRSUtanUndanvindsegel
						log.Printf("GetStarterBoatPrint: Using SRS utan undanvindsegel: %f", srsValue)
					case "srs_shorthanded":
						srsValue = boat.SRSShorthanded
						log.Printf("GetStarterBoatPrint: Using SRS shorthanded: %f", srsValue)
					case "srs_shorthanded_utan_undanvindsegel":
						srsValue = boat.SRSShorthandedUtanUndanvindsegel
						log.Printf("GetStarterBoatPrint: Using SRS shorthanded utan undanvindsegel: %f", srsValue)
					default:
						srsValue = boat.SRS
						log.Printf("GetStarterBoatPrint: Using standard SRS: %f", srsValue)
					}
				}

				// Skip invalid SRS values
				if srsValue <= 0 {
					log.Printf("GetStarterBoatPrint: Skipping participant with invalid SRS value: %f", srsValue)
					continue
				}

				// Update lowest SRS if needed
				if srsValue < lowestSRS {
					lowestSRS = srsValue
				}

				// Add to valid participants
				validParticipants = append(validParticipants, struct {
					Participant models.EventParticipant
					Sailor      models.Sailor
					Boat        models.Boat
					SRSValue    float64
				}{
					Participant: p,
					Sailor:      sailor,
					Boat:        boat,
					SRSValue:    srsValue,
				})
			}

			// Calculate start times for each participant
			for _, vp := range validParticipants {
				// Calculate the start time offset in seconds with multiplier support
				multiplier := event.GetJaktstartMultiplier()
				offsetSeconds := utils.CalculateStartTimeWithMultiplier(float64(event.Banlangd), event.Vind, vp.SRSValue, lowestSRS, multiplier)

				// Format the offset as a human-readable string
				offsetStr := utils.FormatStartTime(offsetSeconds)

				// Calculate the absolute start time
				absoluteStartTime := utils.CalculateAbsoluteStartTime(baseStartTime, offsetSeconds)
				absoluteStartTimeStr := utils.FormatAbsoluteStartTime(absoluteStartTime)

				// Update the participant's SRS value for display
				updatedParticipant := vp.Participant
				updatedParticipant.SelectedSRSValue = vp.SRSValue

				// Add to the list
				enrichedParticipants = append(enrichedParticipants, ParticipantWithStartTime{
					EventParticipant:  updatedParticipant,
					StartTimeOffset:   offsetStr,
					AbsoluteStartTime: absoluteStartTimeStr,
					Sailor:            vp.Sailor,
					Boat:              vp.Boat,
				})
			}

			// Sort participants by start time (earliest first)
			sort.Slice(enrichedParticipants, func(i, j int) bool {
				timeI, _ := time.Parse("15:04", enrichedParticipants[i].AbsoluteStartTime)
				timeJ, _ := time.Parse("15:04", enrichedParticipants[j].AbsoluteStartTime)
				return timeI.Before(timeJ)
			})
		} else {
			// Regular race, no jaktstart
			for _, p := range participants {
				sailor, err := h.DB.GetSailor(p.SailorID)
				if err != nil {
					continue
				}

				var boat models.Boat
				// For entypsegling events, participants don't have boats
				if !event.Entypsegling {
					boat, err = h.DB.GetBoat(p.BoatID)
					if err != nil {
						continue
					}
				}

				// Get the SRS value to use for display
				var srsValue float64
				if p.UseCustomSRSValue && p.CustomSRSValue > 0 {
					// If using a custom SRS value, use it
					srsValue = p.CustomSRSValue
				} else {
					// Always use the boat's SRS value based on the SRS type
					switch p.SRSType {
					case "srs_utan_undanvindsegel":
						srsValue = boat.SRSUtanUndanvindsegel
					case "srs_shorthanded":
						srsValue = boat.SRSShorthanded
					case "srs_shorthanded_utan_undanvindsegel":
						srsValue = boat.SRSShorthandedUtanUndanvindsegel
					default:
						srsValue = boat.SRS
					}
				}

				// Update the participant's SRS value for display
				updatedParticipant := p
				updatedParticipant.SelectedSRSValue = srsValue

				enrichedParticipants = append(enrichedParticipants, ParticipantWithStartTime{
					EventParticipant: updatedParticipant,
					Sailor:           sailor,
					Boat:             boat,
				})
			}

			// Sort participants by sail number
			sort.Slice(enrichedParticipants, func(i, j int) bool {
				return enrichedParticipants[i].Boat.Segelnummer < enrichedParticipants[j].Boat.Segelnummer
			})
		}
	}

	// Get current time for the footer
	now := time.Now()

	log.Printf("GetStarterBoatPrint: Rendering template with %d enriched participants", len(enrichedParticipants))
	for i, p := range enrichedParticipants {
		log.Printf("GetStarterBoatPrint: Enriched participant %d: Sailor=%s, Boat=%s, SRS=%f",
			i+1, p.Sailor.Namn, p.Boat.Battyp, p.EventParticipant.SelectedSRSValue)
	}

	// For entypsegling events, get heats information
	var heats []models.Heat
	if event.Entypsegling {
		var err error
		heats, err = h.DB.GetEventHeats(id)
		if err != nil {
			log.Printf("GetStarterBoatPrint: Error getting heats for entypsegling event: %v", err)
			// Continue without heats - template will handle empty heats
		}
		log.Printf("GetStarterBoatPrint: Found %d heats for entypsegling event", len(heats))
	}

	// Set appropriate title based on event type
	title := "Startbåtsinformation - " + event.Namn
	if event.Entypsegling {
		title = "Placeringsformulär - " + event.Namn
	}

	c.HTML(http.StatusOK, "starter_boat_print.html", gin.H{
		"title":             title,
		"event":             event,
		"participants":      enrichedParticipants,
		"heats":             heats,
		"now":               now,
		"add":               func(i, j int) int { return i + j }, // Helper function for templates
		"use24hTime":        h.getTimeFormatSetting(),
		"usingSavedResults": usingSavedResults,
		"isJaktstart":       event.IsJaktstart(),     // Helper for template
		"isHalfJaktstart":   event.IsHalfJaktstart(), // Helper for template
		"isEntypsegling":    event.IsEntypsegling(),  // Helper for template
	})
}

// GetResultsPrint renders the print-friendly results page
func (h *Handler) GetResultsPrint(c *gin.Context) {
	// Get all events for the dropdown (do this first to ensure we have events for the template)
	allEvents, err := h.DB.GetEvents()
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Error fetching events: " + err.Error(),
		})
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		// If the ID is invalid, redirect to the events page
		c.Redirect(http.StatusSeeOther, "/events")
		return
	}

	event, err := h.DB.GetEvent(id)
	if err != nil {
		// If the event doesn't exist, show a more user-friendly error
		c.HTML(http.StatusOK, "events.html", gin.H{
			"title":                  "Seglingstävlingar",
			"events":                 allEvents,
			"allEvents":              allEvents,
			"activeMenu":             "list",
			"error":                  "Tävlingen kunde inte hittas. Välj en annan tävling.",
			"selectedEventJaktstart": false,
		})
		return
	}

	// Get heats for this event
	heats, err := h.DB.GetEventHeats(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get selected heat from query parameter
	selectedHeatIDStr := c.Query("heat")
	var selectedHeat models.Heat
	var showOverall bool = false
	var isMultiHeat bool = len(heats) > 1

	if selectedHeatIDStr != "" {
		if selectedHeatIDStr == "overall" {
			showOverall = true
		} else {
			selectedHeatID, err := strconv.ParseInt(selectedHeatIDStr, 10, 64)
			if err == nil {
				for _, heat := range heats {
					if heat.ID == selectedHeatID {
						selectedHeat = heat
						break
					}
				}
			}
		}
	}

	// Determine what to show based on event type and selection
	if isMultiHeat {
		// For multi-heat events, default to overall if no specific heat selected
		if selectedHeat.ID == 0 && !showOverall {
			showOverall = true
		}
	} else {
		// For single-heat events, always show the single heat
		if len(heats) > 0 {
			selectedHeat = heats[0]
		}
		showOverall = false
	}

	// Variable to store results
	var savedResults []models.SavedResult
	var usingSavedResults bool
	var totalResults []models.TotalResult
	var heatResults []models.HeatResult

	// Get the appropriate results based on selection
	if event.Locked {
		log.Printf("GetResultsPrint: Event is locked, getting saved results")
		savedResults, err = h.DB.GetSavedResults(id)
		if err != nil {
			log.Printf("GetResultsPrint: Error getting saved results: %v", err)
			// Fall back to calculated results
			event.Locked = false // Temporarily treat as unlocked for fallback
		} else {
			usingSavedResults = true
			log.Printf("GetResultsPrint: Found %d saved results for event", len(savedResults))
		}
	}

	if !event.Locked {
		// Calculate results based on selection
		if showOverall && isMultiHeat {
			log.Printf("GetResultsPrint: Getting overall results for multi-heat event")
			totalResults, err = h.DB.GetEventTotalResults(id)
			if err != nil {
				c.HTML(http.StatusInternalServerError, "error.html", gin.H{
					"error": err.Error(),
				})
				return
			}
			log.Printf("GetResultsPrint: Found %d total results", len(totalResults))
		} else if selectedHeat.ID != 0 {
			log.Printf("GetResultsPrint: Getting results for heat %d", selectedHeat.ID)
			heatResults, err = h.DB.GetHeatResultsComplete(selectedHeat.ID)
			if err != nil {
				c.HTML(http.StatusInternalServerError, "error.html", gin.H{
					"error": err.Error(),
				})
				return
			}
			log.Printf("GetResultsPrint: Found %d heat results", len(heatResults))
		} else {
			log.Printf("GetResultsPrint: No valid heat or overall selection")
			heatResults = []models.HeatResult{}
		}
	}

	// Check if Google Drive integration is enabled
	googleDriveEnabled, err := h.DB.GetSetting("google_drive_enabled")
	if err != nil {
		googleDriveEnabled = "false" // Default to disabled if setting not found
	}

	// Prepare the template data
	templateData := gin.H{
		"title":              "Resultat - " + event.Namn,
		"event":              event,
		"add":                func(i, j int) int { return i + j }, // Helper function for templates
		"use24hTime":         h.getTimeFormatSetting(),
		"googleDriveEnabled": googleDriveEnabled == "true",
		"isMultiHeat":        isMultiHeat,
		"heats":              heats,
		"showOverall":        showOverall,
		"selectedHeat":       selectedHeat,
		"usingSavedResults":  usingSavedResults,
	}

	// Add the appropriate results data based on selection
	if usingSavedResults {
		templateData["savedResults"] = savedResults
	} else if showOverall && isMultiHeat {
		templateData["totalResults"] = totalResults
	} else if selectedHeat.ID != 0 {
		templateData["heatResults"] = heatResults
	}

	c.HTML(http.StatusOK, "results_print.html", templateData)
}

// UpdateDiscardSetting updates the discard_after_heats setting for an event
func (h *Handler) UpdateDiscardSetting(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Ogiltigt event-ID",
		})
		return
	}

	// Get the discard_after_heats value from the form
	discardAfterHeatsStr := c.PostForm("discard_after_heats")
	discardAfterHeats, err := strconv.Atoi(discardAfterHeatsStr)
	if err != nil || discardAfterHeats < 0 {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Ogiltigt värde för borträkning efter antal deltävlingar",
		})
		return
	}

	// Get the current event
	event, err := h.DB.GetEvent(id)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Update the event with the new discard setting
	err = h.DB.UpdateEventWithDiscardSupport(
		id,
		event.Namn,
		event.Datum,
		event.Starttid,
		event.Vind,
		event.Banlangd,
		event.JaktstartType,
		event.Tavlingstyp,
		event.Beskrivning,
		event.BoatType,
		discardAfterHeats,
	)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": err.Error(),
		})
		return
	}

	// Redirect back to the results page
	c.Redirect(http.StatusSeeOther, fmt.Sprintf("/events/%d/results", id))
}

// convertSavedResultsToParticipants converts saved results to ParticipantWithStartTime format
// for use in the starter boat information page
func (h *Handler) convertSavedResultsToParticipants(savedResults []models.SavedResult, event models.Event) []ParticipantWithStartTime {
	var enrichedParticipants []ParticipantWithStartTime

	// Create a dummy boat and sailor for each saved result
	for _, result := range savedResults {
		// Create a dummy boat with the saved values
		boat := models.Boat{
			ID:                               result.BoatID,
			Namn:                             result.BoatName,
			Battyp:                           result.BoatType,
			MatbrevsNummer:                   result.MatbrevsNummer,
			Segelnummer:                      result.Segelnummer,
			Nationality:                      result.Nationality,
			SRS:                              result.SRSValue, // Use the saved SRS value for all SRS fields
			SRSUtanUndanvindsegel:            result.SRSValue,
			SRSShorthanded:                   result.SRSValue,
			SRSShorthandedUtanUndanvindsegel: result.SRSValue,
		}

		// Create a dummy sailor with the saved values
		sailor := models.Sailor{
			ID:    result.SailorID,
			Namn:  result.SailorName,
			Klubb: result.SailorClub,
		}

		// Create a dummy event participant
		participant := models.EventParticipant{
			ID:                int64(result.Position), // Use position as ID
			EventID:           result.EventID,
			SailorID:          result.SailorID,
			BoatID:            result.BoatID,
			SRSType:           result.SRSType,
			SelectedSRSValue:  result.SRSValue,
			UseCustomSRSValue: result.UseCustomSRSValue, // Add this field
			CustomSRSValue:    result.SRSValue,          // Use the saved SRS value as custom value too
			FinishTime:        result.FinishTime,
			CrewCount:         result.CrewCount,
		}

		// Calculate start time for jaktstart events
		var absoluteStartTime string
		var startTimeOffset string

		if event.IsJaktstart() {
			// Use the saved start time
			absoluteStartTime = result.StartTime
		}

		// Add to the list
		enrichedParticipants = append(enrichedParticipants, ParticipantWithStartTime{
			EventParticipant:  participant,
			StartTimeOffset:   startTimeOffset,
			AbsoluteStartTime: absoluteStartTime,
			Sailor:            sailor,
			Boat:              boat,
		})
	}

	// Sort participants appropriately
	if event.Jaktstart {
		// Sort by start time for jaktstart events
		sort.Slice(enrichedParticipants, func(i, j int) bool {
			timeI, _ := time.Parse("15:04", enrichedParticipants[i].AbsoluteStartTime)
			timeJ, _ := time.Parse("15:04", enrichedParticipants[j].AbsoluteStartTime)
			return timeI.Before(timeJ)
		})
	} else {
		// Sort by position (which is stored in the ID field)
		sort.Slice(enrichedParticipants, func(i, j int) bool {
			return enrichedParticipants[i].EventParticipant.ID < enrichedParticipants[j].EventParticipant.ID
		})
	}

	return enrichedParticipants
}

// ExportResultsCSV exports the results as a CSV file
func (h *Handler) ExportResultsCSV(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		// If the ID is invalid, redirect to the events page
		c.Redirect(http.StatusSeeOther, "/events")
		return
	}

	event, err := h.DB.GetEvent(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tävlingen kunde inte hittas."})
		return
	}

	// Get heats for this event
	heats, err := h.DB.GetEventHeats(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Get selected heat from query parameter
	selectedHeatIDStr := c.Query("heat")
	var selectedHeat models.Heat
	var showOverall bool = false
	var isMultiHeat bool = len(heats) > 1

	if selectedHeatIDStr != "" {
		if selectedHeatIDStr == "overall" {
			showOverall = true
		} else {
			selectedHeatID, err := strconv.ParseInt(selectedHeatIDStr, 10, 64)
			if err == nil {
				for _, heat := range heats {
					if heat.ID == selectedHeatID {
						selectedHeat = heat
						break
					}
				}
			}
		}
	}

	// Determine what to show based on event type and selection
	if isMultiHeat {
		// For multi-heat events, default to overall if no specific heat selected
		if selectedHeat.ID == 0 && !showOverall {
			showOverall = true
		}
	} else {
		// For single-heat events, always show the single heat
		if len(heats) > 0 {
			selectedHeat = heats[0]
		}
		showOverall = false
	}

	// Variable to store results
	var savedResults []models.SavedResult
	var usingSavedResults bool
	var totalResults []models.TotalResult
	var heatResults []models.HeatResult

	// Get the appropriate results based on selection
	if event.Locked {
		log.Printf("ExportResultsCSV: Event is locked, getting saved results")
		savedResults, err = h.DB.GetSavedResults(id)
		if err != nil {
			log.Printf("ExportResultsCSV: Error getting saved results: %v", err)
			// Fall back to calculated results
			event.Locked = false // Temporarily treat as unlocked for fallback
		} else {
			usingSavedResults = true
			log.Printf("ExportResultsCSV: Found %d saved results for event", len(savedResults))
		}
	}

	if !event.Locked {
		// Calculate results based on selection
		if showOverall && isMultiHeat {
			log.Printf("ExportResultsCSV: Getting overall results for multi-heat event")
			totalResults, err = h.DB.GetEventTotalResults(id)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			log.Printf("ExportResultsCSV: Found %d total results", len(totalResults))
		} else if selectedHeat.ID != 0 {
			log.Printf("ExportResultsCSV: Getting results for heat %d", selectedHeat.ID)
			heatResults, err = h.DB.GetHeatResultsComplete(selectedHeat.ID)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			log.Printf("ExportResultsCSV: Found %d heat results", len(heatResults))
		} else {
			log.Printf("ExportResultsCSV: No valid heat or overall selection")
			heatResults = []models.HeatResult{}
		}
	}

	// Set filename and headers for download - include heat information
	var filename string
	if showOverall && isMultiHeat {
		filename = fmt.Sprintf("resultat_%s_%s_totala.csv", event.Namn, event.Datum.Format("2006-01-02"))
	} else if selectedHeat.ID != 0 && isMultiHeat {
		// Only include heat name for multi-heat events
		cleanHeatName := strings.ReplaceAll(selectedHeat.Name, " ", "_")
		cleanHeatName = strings.ReplaceAll(cleanHeatName, "/", "_")
		cleanHeatName = strings.ReplaceAll(cleanHeatName, "\\", "_")
		filename = fmt.Sprintf("resultat_%s_%s_%s.csv", event.Namn, event.Datum.Format("2006-01-02"), cleanHeatName)
	} else {
		// Single heat events or no specific heat selected
		filename = fmt.Sprintf("resultat_%s_%s.csv", event.Namn, event.Datum.Format("2006-01-02"))
	}
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))

	// Create CSV writer
	writer := csv.NewWriter(c.Writer)
	// Set the separator to comma (more widely supported)
	writer.Comma = ','
	defer writer.Flush()

	// Write header row - dynamic based on what we're exporting and event type
	var headers []string
	if showOverall && isMultiHeat {
		// Multi-heat total results header
		if event.Entypsegling {
			// Entypsegling total results: Placering, Seglare, Segelnummer, Besättning, Totala poäng, Heat columns
			headers = []string{
				"Placering",
				"Seglare",
				"Segelnummer",
				"Besättning",
				"Totala poäng",
			}
		} else {
			// Regular sailing total results: show boat info, SRS, etc.
			headers = []string{
				"Placering",
				"Seglare",
				"Klubb",
				"Båt",
				"Båttyp",
				"Mätbrevsnummer",
				"SRS-värde",
				"SRS-typ",
				"Besättning",
				"Totala poäng",
			}
		}
		// Add columns for each heat
		for _, heat := range heats {
			headers = append(headers, heat.Name)
		}
	} else {
		// Single heat or specific heat results header
		if event.Entypsegling {
			// Entypsegling individual heat: Plats, Seglare, Klubb, Segelnummer, Besättning, Poäng
			headers = []string{
				"Plats",
				"Seglare",
				"Klubb",
				"Segelnummer",
				"Besättning",
				"Poäng",
			}
		} else {
			// Regular sailing individual heat: show boat info, SRS, times, etc.
			headers = []string{
				"Placering",
				"Seglare",
				"Klubb",
				"Båt",
				"Båttyp",
				"Mätbrevsnummer",
				"SRS-värde",
				"SRS-typ",
				"Besättning",
				"Starttid",
				"Måltid",
				"Seglad tid",
				"Korrigerad tid",
				"Efter föregående",
				"Efter vinnare",
			}
			// Add points column for heat results
			if selectedHeat.ID != 0 && isMultiHeat {
				headers = append(headers, "Poäng")
			}
		}
	}

	if err := writer.Write(headers); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Kunde inte skapa CSV-fil: " + err.Error()})
		return
	}

	// Note: No comment/explanation row in CSV to avoid import issues
	// The (B) suffix in individual cells provides sufficient information

	// Write data rows
	if usingSavedResults {
		// Write saved results
		for _, result := range savedResults {
			// Format SRS value and type for display (same logic as results page)
			var srsValue string
			var srsTypeDisplay string
			if result.UseCustomSRSValue {
				srsValue = fmt.Sprintf("%.3f", result.SRSValue)
				srsTypeDisplay = "anpassad"
			} else {
				srsValue = fmt.Sprintf("%.3f", result.SRSValue)
				switch result.SRSType {
				case "srs":
					srsTypeDisplay = "SRS"
				case "srs_utan_undanvindsegel":
					srsTypeDisplay = "SRS utan undanvindsegel"
				case "srs_shorthanded":
					srsTypeDisplay = "SRS S/H"
				case "srs_shorthanded_utan_undanvindsegel":
					srsTypeDisplay = "SRS S/H utan undanvindsegel"
				default:
					srsTypeDisplay = "Standard"
				}
			}

			// Format sail number with nationality
			sailNumber := ""
			if result.Segelnummer != "" {
				if result.Nationality != "" {
					sailNumber = result.Nationality + "-" + result.Segelnummer
				} else {
					sailNumber = result.Segelnummer
				}
			}

			row := []string{
				strconv.Itoa(result.Position),      // Placering
				result.SailorName,                  // Seglare
				result.SailorClub,                  // Klubb
				result.BoatName,                    // Båt
				result.BoatType,                    // Båttyp
				sailNumber,                         // Segelnummer
				result.MatbrevsNummer,              // Mätbrevsnummer
				srsValue,                           // SRS-värde
				srsTypeDisplay,                     // SRS-typ
				strconv.Itoa(result.CrewCount + 1), // Besättning (crew + skipper)
				result.StartTime,                   // Starttid
				result.FinishTime,                  // Måltid
				result.ElapsedTime,                 // Seglad tid
				result.CorrectedTime,               // Korrigerad tid
				result.TimeToPrevious,              // Efter föregående
				result.TimeToWinner,                // Efter vinnare
			}

			if err := writer.Write(row); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Kunde inte skriva till CSV-fil: " + err.Error()})
				return
			}
		}
	} else if showOverall && isMultiHeat {
		// Write multi-heat total results
		for _, result := range totalResults {
			var row []string

			if event.Entypsegling {
				// Entypsegling total results: Placering, Seglare, Segelnummer, Besättning, Totala poäng, Heat columns
				row = []string{
					strconv.Itoa(result.TotalPosition),                  // Placering
					result.Sailor.Namn,                                  // Seglare
					result.EventParticipant.PersonalNumber,              // Segelnummer
					strconv.Itoa(result.EventParticipant.CrewCount + 1), // Besättning (crew + skipper)
					fmt.Sprintf("%.1f", result.TotalPoints),             // Totala poäng
				}
			} else {
				// Regular sailing total results: show boat info, SRS, etc.
				// Format SRS value and type for display (same logic as results page)
				var srsValue string
				var srsTypeDisplay string
				if result.EventParticipant.UseCustomSRSValue {
					srsValue = fmt.Sprintf("%.3f", result.EventParticipant.CustomSRSValue)
					srsTypeDisplay = "anpassad"
				} else {
					srsValue = fmt.Sprintf("%.3f", result.EventParticipant.SelectedSRSValue)
					switch result.EventParticipant.SRSType {
					case "srs":
						srsTypeDisplay = "SRS"
					case "srs_utan_undanvindsegel":
						srsTypeDisplay = "SRS utan undanvindsegel"
					case "srs_shorthanded":
						srsTypeDisplay = "SRS S/H"
					case "srs_shorthanded_utan_undanvindsegel":
						srsTypeDisplay = "SRS S/H utan undanvindsegel"
					default:
						srsTypeDisplay = "Standard"
					}
				}

				row = []string{
					strconv.Itoa(result.TotalPosition), // Placering
					result.Sailor.Namn,                 // Seglare
					result.Sailor.Klubb,                // Klubb
					result.Boat.Namn,                   // Båt
					result.Boat.Battyp,                 // Båttyp
					result.Boat.MatbrevsNummer,         // Mätbrevsnummer
					srsValue,                           // SRS-värde
					srsTypeDisplay,                     // SRS-typ
					strconv.Itoa(result.EventParticipant.CrewCount + 1), // Besättning (crew + skipper)
					fmt.Sprintf("%.1f", result.TotalPoints),             // Totala poäng
				}
			}

			// Add individual heat results
			for _, heat := range heats {
				found := false
				for _, heatResult := range result.HeatResults {
					if heatResult.Heat.ID == heat.ID {
						found = true
						// Check if this heat is discarded
						isDiscarded := false
						for _, discardedHeatID := range result.DiscardedHeats {
							if discardedHeatID == heat.ID {
								isDiscarded = true
								break
							}
						}

						var cellValue string
						if heatResult.DNS {
							cellValue = fmt.Sprintf("DNS (%.1fp)", heatResult.Points)
						} else if heatResult.DNF {
							cellValue = fmt.Sprintf("DNF (%.1fp)", heatResult.Points)
						} else {
							cellValue = fmt.Sprintf("%d (%.1fp)", heatResult.Position, heatResult.Points)
						}

						// Mark discarded heats with suffix
						if isDiscarded {
							cellValue = cellValue + " (B)"
						}

						row = append(row, cellValue)
						break
					}
				}
				if !found {
					row = append(row, "-")
				}
			}

			if err := writer.Write(row); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Kunde inte skriva till CSV-fil: " + err.Error()})
				return
			}
		}
	} else if selectedHeat.ID != 0 {
		// Write heat results
		for _, result := range heatResults {
			// Position handling for DNS/DNF
			position := ""
			if result.DNS {
				position = "DNS"
			} else if result.DNF {
				position = "DNF"
			} else {
				position = strconv.Itoa(result.Position)
			}

			var row []string

			if event.Entypsegling {
				// Entypsegling individual heat: Plats, Seglare, Klubb, Segelnummer, Besättning, Poäng
				row = []string{
					position,                               // Plats
					result.Sailor.Namn,                     // Seglare
					result.Sailor.Klubb,                    // Klubb
					result.EventParticipant.PersonalNumber, // Segelnummer
					strconv.Itoa(result.TotalPersons),      // Besättning
					fmt.Sprintf("%.1f", result.Points),     // Poäng
				}
			} else {
				// Regular sailing individual heat: show boat info, SRS, times, etc.
				// Format SRS value and type for display (same logic as results page)
				var srsValue string
				var srsTypeDisplay string
				if result.EventParticipant.UseCustomSRSValue {
					srsValue = fmt.Sprintf("%.3f", result.EventParticipant.CustomSRSValue)
					srsTypeDisplay = "anpassad"
				} else {
					srsValue = fmt.Sprintf("%.3f", result.EventParticipant.SelectedSRSValue)
					switch result.EventParticipant.SRSType {
					case "srs":
						srsTypeDisplay = "SRS"
					case "srs_utan_undanvindsegel":
						srsTypeDisplay = "SRS utan undanvindsegel"
					case "srs_shorthanded":
						srsTypeDisplay = "SRS S/H"
					case "srs_shorthanded_utan_undanvindsegel":
						srsTypeDisplay = "SRS S/H utan undanvindsegel"
					default:
						srsTypeDisplay = "Standard"
					}
				}

				row = []string{
					position,                          // Placering
					result.Sailor.Namn,                // Seglare
					result.Sailor.Klubb,               // Klubb
					result.Boat.Namn,                  // Båt
					result.Boat.Battyp,                // Båttyp
					result.Boat.MatbrevsNummer,        // Mätbrevsnummer
					srsValue,                          // SRS-värde
					srsTypeDisplay,                    // SRS-typ
					strconv.Itoa(result.TotalPersons), // Besättning
					result.StartTime,                  // Starttid
					result.FinishTime,                 // Måltid
					result.ElapsedTime,                // Seglad tid
					result.CorrectedTime,              // Korrigerad tid
					result.TimeToPrevious,             // Efter föregående
					result.TimeToWinner,               // Efter vinnare
				}

				// Add points column for multi-heat events
				if isMultiHeat {
					row = append(row, fmt.Sprintf("%.1f", result.Points))
				}
			}

			if err := writer.Write(row); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Kunde inte skriva till CSV-fil: " + err.Error()})
				return
			}
		}
	}
}
